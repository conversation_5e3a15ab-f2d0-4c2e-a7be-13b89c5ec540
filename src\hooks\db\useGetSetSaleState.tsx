import { useState, useCallback } from 'react';
import axios from 'axios';

export const useGetSetSaleState = () => {
    const [getData, setGetData] = useState<any>();
    const [getIsLoading, setGetIsLoading] = useState<boolean>(false);
    const [getError, setGetError] = useState<string | null>(null);

    const [setData, setSetData] = useState<any>();
    const [setIsLoading, setSetIsLoading] = useState<boolean>(false);
    const [setError, setSetError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    const getSaleState = useCallback(async (sale_id: string) => {
        setGetIsLoading(true);
        setGetError(null);

        try {
            const headers = {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            }

            const response = await axios.get(`${apiUrl}/crm/sale/state/${sale_id}`, { headers });
            console.log(response)
            setGetData(response.data);
        } catch (error: any) {
            setGetError(error.message);
        } finally {
            setGetIsLoading(false);
        }
    }, []);

    const setSaleState = useCallback(async (sale_id: string, status: string) => {
        setSetIsLoading(true);
        setSetError(null);

        try {
            const headers = {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            }

            const response = await axios.post(`${apiUrl}/crm/sale/state/${sale_id}`, { status }, { headers });

            setSetData(response.data);
        } catch (error: any) {
            setSetError(error.message);
        } finally {
            setSetIsLoading(false);
        }
    }, []);

    return { getData, getIsLoading, getError, getSaleState, setData, setIsLoading, setError, setSaleState };
};
