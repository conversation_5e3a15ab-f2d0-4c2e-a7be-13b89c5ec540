import { useState, useCallback } from 'react';
import { CustomerData } from '../../types/customer';

interface QueryResult {
    [key: string]: any;
}

export const useCustomers = () => {
    const [data, setData] = useState<CustomerData[]>([]);
    const apiUrl = import.meta.env.VITE_API_URL;
    const token = localStorage.getItem('token');

    const sendCustomerRequest = async (): Promise<void> => {
        try {
            const response = await fetch(`${apiUrl}/crm/customers`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Error: ${response.status}`);
            }
            
            const responseData: QueryResult[] = await response.json();
            // console.log('Query result:', responseData);

            const formattedData: CustomerData[] = responseData.map(item => ({
                id: item.uuid,
                first_name: item.first_name,
                contact_id: item.contact_id,
                national_id: item.national_id,
                mobile_phone: item.mobile_phone,
                email: item.email,
                status : item.status,
                address: "item.address",
                postal_code: "item.postal_code",
                city: "item.city",
                province: "item.province",
                bank_account: "item.bank_account",
                billing_address: "item.billing_address",
                billing_postal_code: "item.billing_postal_code",
                billing_city: "item.billing_city",
                billing_province: "item.billing_province",
                shipping_address: "item.shipping_address",
                shipping_postal_code: "item.shipping_postal_code",
                shipping_city: "item.shipping_city",
                shipping_province: "item.shipping_province"
            }));

            setData(formattedData);

        } catch (error) {
            console.error('Error fetching customers:', error);
        }
    }

    const sendSpecificCustomerRequest = useCallback(async (id: string): Promise<void> => {
        try {
            const formData = new FormData();
            formData.append('uuid', id);
    
            const response = await fetch(`${apiUrl}/crm/customer`, {
                method: 'POST',
                body: formData
            });
    
            if (!response.ok) {
                throw new Error(`Error: ${response.status}`);
            }
            
            const responseData = await response.json();

            // If responseData is a single object
            const formattedData = {
                id: responseData.uuid,
                first_name: responseData.first_name,
                contact_id: responseData.contact_id,
                national_id: responseData.national_id,
                mobile_phone: responseData.mobile_phone,
                email: responseData.email,
                status: responseData.status,
                address: responseData.address,
                postal_code: responseData.postal_code,
                city: responseData.city,
                province: responseData.province,
                bank_account: responseData.bank_account,
                billing_address: responseData.billing_address,
                billing_postal_code: responseData.billing_postal_code,
                billing_city: responseData.billing_city,
                billing_province: responseData.billing_province,
                shipping_address: responseData.shipping_address,
                shipping_postal_code: responseData.shipping_postal_code,
                shipping_city: responseData.shipping_city,
                shipping_province: responseData.shipping_province,
            };

            setData([formattedData]);

        } catch (error) {
            console.error('Error fetching customers:', error);
        }
    }, []);

    return { data, sendCustomerRequest, sendSpecificCustomerRequest };
};