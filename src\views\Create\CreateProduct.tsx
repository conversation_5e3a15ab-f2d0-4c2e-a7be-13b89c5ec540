import React, { useState, useEffect, ChangeEvent, FormEvent } from 'react';
import { useCreateProduct } from '../../hooks/creates/useProductsCreate';
import Modal from 'react-modal';

import { Toaster } from 'react-hot-toast';

interface ProductData {
    product_id: string;
    name: string;
    description: string;
    iva: number;
    price_with_vat: number;
    price: number;
    is_recurring: number;
    recurrence_period: number;
    speed: number;
    data_bonus: number;
    sms_count: number;
    voice_calls: number;
    product_type: string;
    line_type: string;
    provider: string;
    status: boolean;
    is_filtered: number;
}

const productFields: { label: string, name: keyof ProductData, placeholder: string, type?: string }[] = [
    { label: 'Product Name', name: 'name', placeholder: 'Enter product name' },
    { label: 'Description', name: 'description', placeholder: 'Enter product description' },
    { label: 'IVA', name: 'iva', placeholder: 'Enter IVA', type: 'number' },
    { label: 'Price', name: 'price', placeholder: 'Enter price', type: 'number' },
    { label: 'VAT Price', name: 'price_with_vat', placeholder: 'Enter VAT price', type: 'number' },
    { label: 'Recurring', name: 'is_recurring', placeholder: '', type: 'checkbox' },
    { label: 'Recurrence Period', name: 'recurrence_period', placeholder: 'Enter recurrence period', type: 'number' },
    { label: 'Speed', name: 'speed', placeholder: 'Enter speed', type: 'number' },
    { label: 'Data Bonus', name: 'data_bonus', placeholder: 'Enter data bonus', type: 'number' },
    { label: 'SMS Count', name: 'sms_count', placeholder: 'Enter SMS count', type: 'number' },
    { label: 'Voice Calls', name: 'voice_calls', placeholder: 'Enter voice calls', type: 'number' },
    { label: 'Product Type', name: 'product_type', placeholder: 'Enter product type' },
    { label: 'Line Type', name: 'line_type', placeholder: 'Enter line type' },
    { label: 'Provider', name: 'provider', placeholder: 'Enter provider' },
    { label: 'Status', name: 'status', placeholder: '', type: 'checkbox' },
    { label: 'Is Filter', name: 'is_filtered', placeholder: '', type: 'checkbox' },
];

const CreateProduct: React.FC = () => {
    const [token] = useState<string | null>(localStorage.getItem('token'));
    const { createProduct } = useCreateProduct(token);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [formData, setFormData] = useState<ProductData>({
        product_id: '',
        name: '',
        description: '',
        iva: 21,
        price_with_vat: 0,
        price: 0,
        is_recurring: 0,
        recurrence_period: 0,
        speed: 0,
        data_bonus: 0,
        sms_count: 0,
        voice_calls: 0,
        product_type: '',
        line_type: '',
        provider: '',
        status: false,
        is_filtered: 0
    });

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name, value, type, checked } = e.target;
        setFormData(prevFormData => ({
            ...prevFormData,
            [name]: type === 'checkbox' ? (checked ? 1 : 0) : value
        }));
    };    

    const handleOpenModal = () => setIsModalOpen(true);
    const handleCloseModal = () => setIsModalOpen(false);

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        handleOpenModal();
    };

    const handleConfirmAndSend = async () => {
        handleCloseModal();
        const { iva, ...productDataWithoutIVA } = formData; // Excluye el campo 'iva'
        await createProduct(productDataWithoutIVA);
    };

    useEffect(() => {
        // Calcula price_with_vat cuando price o iva cambian
        const vatPriceCalculated = formData.price * (1 + formData.iva / 100);
        setFormData(prevFormData => ({
            ...prevFormData,
            price_with_vat: parseFloat(vatPriceCalculated.toFixed(2)) // Redondear a dos decimales
        }));
    }, [formData.price, formData.iva]);
    
    useEffect(() => {
        // Calcula price cuando price_with_vat cambia
        if (formData.iva !== 0) { // Evita la división por cero
            const priceCalculated = formData.price_with_vat / (1 + formData.iva / 100);
            setFormData(prevFormData => ({
                ...prevFormData,
                price: parseFloat(priceCalculated.toFixed(2)) // Redondear a dos decimales
            }));
        }
    }, [formData.price_with_vat, formData.iva]);    

    useEffect(() => {
        // Establecer el valor predeterminado de IVA al cargar el componente
        setFormData(prevFormData => ({
            ...prevFormData,
            iva: prevFormData.iva || 21 // Si iva ya tiene un valor, no se sobrescribe
        }));
    }, []);

    const renderFormSection = (title: string, fields: { label: string, name: keyof ProductData, placeholder: string, type?: string }[]) => (
        <div className="card mb-4">
            <Toaster />
            <div className="card-body">
                <h2 className="card-title">{title}</h2>
                <div className="row">
                    {fields.map(field => {
                        if (field.type === 'checkbox') {
                            return (
                                <div className='ml-3 '>
                                    <div className="mb-3 form-check form-switch" key={field.name}>
                                        <input
                                            type="checkbox"
                                            className="form-check-input"
                                            id={field.name}
                                            name={field.name}
                                            checked={formData[field.name] as unknown as boolean}
                                            onChange={handleChange}
                                        />
                                        <label className="form-check-label ml-2" htmlFor={field.name}>{field.label}</label>
                                    </div>
                                </div>
                            );
                        } else {
                            return (
                                <div className="mb-3" key={field.name}>
                                    <label htmlFor={field.name} className="form-label">{field.label}</label>
                                    <input
                                        type={field.type || "text"}
                                        className="form-control"
                                        id={field.name}
                                        name={field.name}
                                        value={formData[field.name] as unknown as string}
                                        onChange={handleChange}
                                        placeholder={field.placeholder}
                                        required={field.type !== 'checkbox'}
                                    />
                                </div>
                            );
                        }
                    })}
                </div>
            </div>
        </div>
    );

    return (
        <div className="container mt-4 py-5">
            <form onSubmit={handleSubmit}>
                {renderFormSection("Product Details", productFields.filter(field => field.name === 'name' || field.name === 'description'))}
                {renderFormSection("Pricing", productFields.filter(field => field.name === 'iva' || field.name === 'price' || field.name === 'price_with_vat'))}
                {renderFormSection("Recurring Details", productFields.filter(field => field.name === 'is_recurring' || field.name === 'recurrence_period'))}
                {renderFormSection("Product Specifications", productFields.filter(field => field.name === 'speed' || field.name === 'data_bonus' || field.name === 'sms_count' || field.name === 'voice_calls'))}
                {renderFormSection("Product Type and Provider", productFields.filter(field => field.name === 'product_type' || field.name === 'line_type' || field.name === 'provider'))}
                {renderFormSection("Additional Details", productFields.filter(field => field.name === 'status' || field.name === 'is_filtered'))}

                <div className="text-center">
                    <button type="submit" className="btn btn-primary">Create Product</button>
                </div>
            </form>

            <Modal
                isOpen={isModalOpen}
                onRequestClose={handleCloseModal}
                contentLabel="Revisar Información del Producto"
                // Aquí puedes personalizar tu modal
            >
                <h2>Revisar Información del Producto</h2>
                {/* Muestra la información del producto aquí */}
                <div>
                    {Object.entries(formData).map(([key, value]) => (
                        <p key={key}>{`${key}: ${value}`}</p>
                    ))}
                </div>
                <div className="text-center">
                    <button className="btn btn-primary mr-2" onClick={handleConfirmAndSend}>Confirm</button>
                    <button className="btn btn-secondary ml-2" onClick={handleCloseModal}>Cancel</button>
                </div>
            </Modal>
        </div>
    );
};

export default CreateProduct;