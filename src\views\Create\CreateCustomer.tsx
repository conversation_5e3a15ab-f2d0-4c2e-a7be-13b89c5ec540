import React, { useState, ChangeEvent, FormEvent } from 'react';
import { CustomerData } from '../../types/customer';
import Modal from 'react-modal';
import { useCustomerCreate } from '../../hooks/create/useCustomerCreate';
import { useNavigate } from 'react-router-dom';
import { isValid } from 'iban';


const personalFields = [
    { label: 'Nombre', name: 'first_name', placeholder: 'Introduce el nombre' },
    { label: 'DNI', name: 'national_id', placeholder: 'Introduce el DNI' },
];

const contactFields = [
    { label: 'Teléfono móvil', name: 'mobile_phone', placeholder: 'Introduce el teléfono móvil' },
    { label: 'Email', name: 'email', placeholder: 'Introduce el email' },
];

const addressFields = [
    { label: 'Dirección', name: 'address', placeholder: 'Introduce la dirección' },
    { label: 'Código postal', name: 'postal_code', placeholder: 'Introduce el código postal' },
    { label: 'Población', name: 'city', placeholder: 'Introduce la población' },
    { label: 'Provincia', name: 'province', placeholder: 'Introduce la provincia' },
];

const shippingFields = [
    { label: 'Dirección de envío', name: 'shipping_address', placeholder: 'Introduce la dirección de envío' },
    { label: 'Código postal de envío', name: 'shipping_postal_code', placeholder: 'Introduce el código postal de envío' },
    { label: 'Población de envío', name: 'shipping_city', placeholder: 'Introduce la población de envío' },
    { label: 'Provincia de envío', name: 'shipping_province', placeholder: 'Introduce la provincia de envío' },
];

const bankingFields = [
    { label: 'Cuenta bancaria', name: 'bank_account', placeholder: 'Introduce la cuenta bancaria' },
    { label: 'Dirección de facturación', name: 'billing_address', placeholder: 'Introduce la dirección de facturación' },
    { label: 'Código postal de facturación', name: 'billing_postal_code', placeholder: 'Introduce el código postal de facturación' },
    { label: 'Población de facturación', name: 'billing_city', placeholder: 'Introduce la población de facturación' },
    { label: 'Provincia de facturación', name: 'billing_province', placeholder: 'Introduce la provincia de facturación' },
];

const CreateCustomer: React.FC = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const navigate = useNavigate();

    const [formData, setFormData] = useState<CustomerData>({
        id: 0,
        first_name: '',
        contact_id: '',
        national_id: '',
        mobile_phone: '',
        email: '',
        address: '',
        postal_code: '',
        city: '',
        province: '',
        bank_account: '',
        billing_address: '',
        billing_postal_code: '',
        billing_city: '',
        billing_province: '',
        shipping_address: '',
        shipping_postal_code: '',
        shipping_city: '',
        shipping_province: '',
        status: 1,
    });

    const { sendCustomerCreateRequest } = useCustomerCreate();

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        setIsModalOpen(true);
    };

    const confirmCreate = () => {
        setIsModalOpen(false);
        setFormData({ ...formData, id: Date.now() });
        sendCustomerCreateRequest(formData);

        navigate('/customers');
    };

    const copyFields = (fieldName: string) => {
        const fieldGroups = {
            'address': ['address', 'billing_address', 'shipping_address'],
            'postal_code': ['postal_code', 'billing_postal_code', 'shipping_postal_code'],
            'city': ['city', 'billing_city', 'shipping_city'],
            'province': ['province', 'billing_province', 'shipping_province']
        };

        Object.keys(fieldGroups).forEach(key => {
            if (fieldName.includes(key)) {
                let updatedFormData: any = { ...formData };
                (fieldGroups as any)[key].forEach((field: string) => {
                    updatedFormData[field] = (formData as any)[fieldName];
                    const inputField = document.getElementById(field) as HTMLInputElement;
                    inputField.value = (formData as any)[fieldName];
                });

                setFormData(updatedFormData);
            }
        });

        console.log(fieldName);
    }


    const [isIbanValid, setIsIbanValid] = useState(false);
    const calculateIBAN = (countryCode: string, checkDigits: string, bankAccount: string) => {
        const iban = countryCode + checkDigits + bankAccount;
        const isValidIban = isValid(iban);
        console.log(isValidIban);
        setIsIbanValid(isValidIban);
        return isValidIban;
    };


    const renderFormFields = (fields: { label: string, name: string, placeholder: string }[]) => fields.map(field => (
        <div className="col-md-6" key={field.name}>
            <div className="mb-3">
                <label htmlFor={field.name} className="form-label">{field.label}</label>
                <div className='input-group'>
                    <input
                        type="text"
                        className="form-control"
                        id={field.name}
                        name={field.name}
                        onChange={handleChange}
                        placeholder={field.placeholder}
                        required={field.name === 'email'}
                        {
                            ...field.name === 'bank_account' && {
                                style: { color: `${isIbanValid ? 'green' : 'red'}` },
                                // On input change calculate iban
                                onChange: (e: ChangeEvent<HTMLInputElement>) => {
                                    const iban = e.target.value;
                                    const countryCode = iban.slice(0, 2);
                                    const checkDigits = iban.slice(2, 4);
                                    const bankAccount = iban.slice(4);
                                    calculateIBAN(countryCode, checkDigits, bankAccount);
                                }
                            }
                        }
                        {
                        ...field.name === 'national_id' && {
                            pattern: '^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$',
                            required: true
                        }
                        }
                    />
                    {
                        ['Dirección', 'Código postal', 'Población', 'Provincia'].some(label => field.label.startsWith(label)) &&
                        <span
                            className="input-group-text"
                            id="basic-addon1"
                            onClick={() => copyFields(field.name)}
                            style={{ cursor: 'pointer' }}
                        >
                            Copiar
                        </span>
                    }
                </div>
            </div>
        </div>
    ));

    return (
        <div className="container mt-4 py-5">
            <form onSubmit={handleSubmit}>
                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Datos Personales</h2>
                        <div className="row">
                            {renderFormFields(personalFields)}
                        </div>
                    </div>
                </div>

                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Datos de Contacto</h2>
                        <div className="row">
                            {renderFormFields(contactFields)}
                        </div>
                    </div>
                </div>

                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Dirección</h2>
                        <div className="row">
                            {renderFormFields(addressFields)}
                        </div>
                    </div>
                </div>

                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Dirección de Envío</h2>
                        <div className="row">
                            {renderFormFields(shippingFields)}
                        </div>
                    </div>
                </div>

                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Datos Bancarios</h2>
                        <div className="row">
                            {renderFormFields(bankingFields)}
                        </div>
                    </div>
                </div>

                <div className="text-center pb-4">
                    <button type="submit" className="btn btn-primary">Crear cliente</button>
                </div>
            </form>

            <Modal
                isOpen={isModalOpen}
                onRequestClose={() => setIsModalOpen(false)}
                contentLabel="Confirm Customer Creation"
            >
                <div className="modal-content">
                    <div className="modal-header">
                        <h5 className="modal-title">Confirm Customer Creation</h5>
                        <button type="button" className="close" onClick={() => setIsModalOpen(false)}>
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div className="modal-body">
                        <p>Are you sure you want to create this customer?</p>
                        {
                            Object.entries(formData).map(([key, value]) => (
                                <p key={key}><strong>{key}:</strong> {value}</p>
                            ))
                        }
                    </div>

                    <div className="modal-footer">
                        <button type="button" className="btn btn-secondary" onClick={() => setIsModalOpen(false)}>Cancel</button>
                        <button type="button" className="btn btn-primary" onClick={confirmCreate}>Confirm</button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default CreateCustomer;