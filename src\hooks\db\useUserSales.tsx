import { useState, useEffect } from 'react';

import { Sale } from '../../interfaces/sales';


export const useUserSales = (userId: string) => {
    const [data, setData] = useState<Sale[]>([]);
    const [d2, setd2] = useState<any>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;
    const token = localStorage.getItem('token');


    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const response = await fetch(`${apiUrl}/crm/sales/user/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const result = await response.json();

                setd2(result);

                const salesData: Sale[] = result.map((sale: any) => ({
                    saleId: sale.sale_id,
                    date: sale.date.split('T')[0],
                    items: sale.items.map((item: any) => ({
                        itemId: item.id.toString(),
                        name: item.name,
                        price: item.price,
                        type: item.type === 'Sale' ? 'Sale' : 'Bundle',
                        bundleSales: item.bundleSales?.map((bundleSale: any) => ({
                            itemId: bundleSale.id.toString(),
                            name: bundleSale.name,
                            price: bundleSale.price,
                            type: bundleSale.type === 'Sale' ? 'Sale' : 'Bundle',
                        })),
                        date_activation: item.activation_date ? new Date(item.activation_date).toISOString().split('T')[0] : undefined,
                    })),
                }));

                setData(salesData);
            } catch (error: any) {
                setError(error.message);
                setData([{
                    saleId: '0',
                    date: '',
                    items: [{
                        itemId: '0',
                        name: "No items have been found",
                        price: 0,
                        type: 'Sale',
                        bundleSales: [],
                        ip: '',
                        msisdn: '',
                        sale_detail_id: '',
                    }],
                    user_id: '',
                }]);
            } finally {
                setIsLoading(false);
                console.log(data)
            }
        };

        fetchData();
    }, [userId]);

    return { data, isLoading, error, d2 };
};