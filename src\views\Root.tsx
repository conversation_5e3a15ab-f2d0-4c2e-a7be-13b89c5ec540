import React from 'react';
import { Link } from 'react-router-dom';

const Root: React.FC = () => {
    return (
        <div className="container-fluid p-5" style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gridGap: '20px', height: 'auto' }}>
            {/* Tarjeta Clientes */}
            <div className="card h-100 shadow-sm">
                <div className="card-body d-flex flex-column">
                    <h5 className="card-title">Clientes</h5>
                    <p className="card-text">Añade, modifica o elimina clientes.</p>
                    <div className="mt-auto">
                        <Link to="/customers" className="btn btn-primary w-100">Ir a clientes</Link>
                    </div>
                </div>
            </div>

            {/* Tarjeta Productos */}
            <div className="card h-100 shadow-sm">
                <div className="card-body d-flex flex-column">
                    <h5 className="card-title">Productos</h5>
                    <p className="card-text"><PERSON><PERSON><PERSON>, modifica o elimina productos.</p>
                    <div className="mt-auto">
                        <Link to="/products" className="btn btn-primary w-100">Ir a productos</Link>
                    </div>
                </div>
            </div>

            {/* Tarjeta Ventas */}
            <div className="card h-100 shadow-sm">
                <div className="card-body d-flex flex-column">
                    <h5 className="card-title">Ventas</h5>
                    <p className="card-text">Añade, modifica o elimina ventas.</p>
                    <div className="mt-auto">
                        <Link to="/sales" className="btn btn-primary w-100">Ir a ventas</Link>
                    </div>
                </div>
            </div>

            <div className="card h-100 shadow-sm">
                <div className="card-body d-flex flex-column">
                    <h5 className="card-title">Comunicados</h5>
                    <p className="card-text">Manda un cominicado a todos los clientes.</p>
                    <div className="mt-auto">
                        <Link to="/emails/send" className="btn btn-primary w-100">Ir a comunicados</Link>
                    </div>
                </div>
            </div>

            <div className="card h-100 shadow-sm">
                <div className="card-body d-flex flex-column">
                    <h5 className="card-title">Correos</h5>
                    <p className="card-text">Ver todos los correos.</p>
                    <div className="mt-auto">
                        <Link to="/emails/view" className="btn btn-primary w-100">Ir a correos</Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Root;
