import React, { useEffect, useState } from 'react';
import { useAllEmails } from '../../hooks/db/useAllEmails';
import { useNavigate } from 'react-router-dom';

const ViewEmails: React.FC = () => {
    const [eLength, seteLength] = useState(1);
    const { data, isLoading }: { data: { id: string, subject: string }[], isLoading: boolean } = useAllEmails(eLength);
    const navigate = useNavigate();

    useEffect(() => {
        const handleScroll = () => {
            const isBottom = window.innerHeight + window.scrollY >= document.documentElement.scrollHeight;
            if (isBottom && !isLoading) {
                console.log('Scrolled to the bottom');
                seteLength(eLength + 1);
            }
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, [eLength, isLoading]);

    const handleEmailClick = (emailId: string) => {
        navigate(`/emails/view/${emailId}`);
    };

    if (isLoading && eLength === 1) {
        return <div>Loading...</div>;
    }

    return (
        <div className="container">
            <h1 className="my-3">View Emails</h1>

            <div className="list-group">
                {Array.isArray(data) && data.map((email: { id: string, subject: string }, index: number) => (
                    <div key={index} className="list-group-item" style={{ cursor: "pointer"}} onClick={() => handleEmailClick(email.id)}>
                        <h5 className="mb-1">Subject: {email.subject}</h5>
                        <p className="mb-1">ID: {email.id}</p>
                    </div>
                ))}
            </div>

            {isLoading && eLength > 1 && <div>Loading more emails...</div>}
        </div>
    );
}

export default ViewEmails;