import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

interface ProductData {
  product_id: string;
  name: string;
  description: string;
  price_with_vat: number;
  price: number;
  is_recurring: number;
  recurrence_period: number;
  speed: number;
  data_bonus: number;
  sms_count: number;
  voice_calls: number;
  product_type: string;
  line_type: string;
  provider: string;
  status: boolean;
  is_filtered: number;
}

interface UseCreateProductReturnType {
  loading: boolean;
  error: AxiosError | null;
  createProduct: (productData: ProductData) => Promise<void>;
}

export const useCreateProduct = (token: string | null): UseCreateProductReturnType => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);
  const apiUrl = import.meta.env.VITE_API_URL;

  const createProduct = useCallback(async (productData: ProductData) => {
    if (!token) {
      setError(new Error('No token provided') as AxiosError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json', // Cambiado a application/json
      };

      await axios.post(`${apiUrl}/crm/product/create`, productData, { headers }); // productData enviado directamente como JSON

      // Maneja el estado post-creación aquí si es necesario
    } catch (err) {
      setError(err as AxiosError);
    } finally {
      setLoading(false);
    }
  }, [token, apiUrl]); // Dependencias: token y apiUrl

  return { loading, error, createProduct };
};
