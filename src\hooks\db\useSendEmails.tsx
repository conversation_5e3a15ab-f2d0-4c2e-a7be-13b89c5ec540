import { useState } from 'react';
import axios from 'axios';

export const useSendEmails = () => {
    const [isLoading, setIsLoading] = useState(false); // No es necesario el tipo <boolean> aquí, ya que useState infiere el tipo.
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    const sendEmails = async (emailData: {
      subject: string;
      body: string;
      attachments: File[];
      destinations: { name: string; email: string; uuid: string; }[];
    }) => {
        setIsLoading(true);
        setError(null);

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error("Authentication token is missing");
            }
            const headers = {
                'Authorization': `Bearer ${token}`,
            };

            const formData = new FormData();
            emailData.attachments.forEach((attachment) => {
                formData.append('attachments', attachment, attachment.name); // Asegúrate de que cada archivo tiene un nombre.
            });
            formData.append('subject', emailData.subject);
            formData.append('body', emailData.body);
            formData.append('destinations', JSON.stringify(emailData.destinations)); // Asumiendo que el backend espera un JSON string.

            // Enviar la solicitud POST con FormData
            const response = await axios.post(`${apiUrl}/crm/send-emails`, formData, { headers });
            console.log('Response:', response);
            return response.data; // Podrías querer devolver algo aquí.
        } catch (error) {
            console.error('Error:', error);
            setError((error as any).response?.data?.message || (error as any).message);
            return null; // Devuelve null o maneja el error como sea apropiado.
        } finally {
            setIsLoading(false);
        }
    };

    return { sendEmails, isLoading, error };
};
