export interface SaleItem {
    itemId: string;
    name: string;
    price: number;
    type: 'Sale' | 'Bundle';
    bundleSales?: SaleItem[];
    ip: string;
    msisdn: string;
    sale_detail_id: string;
    date_activation?: string;
}

export interface Sale {
    saleId: string;
    date: string; // assuming date in YYYY-MM-DD format for simplicity
    items: SaleItem[];
    user_id: string;
}

export interface Sales {
    sale_id: string;
    first_name: string;
    date: string;
    total_price: number;
}

export interface SalesDetail {
    id: number;
    sale_id: number;
    item_id: number;
    ip: string | null;
    msisdn: string | null;
    user_name: string;
    contact_id: string;
    item_name: string;
    user_uuid: string;
}

export interface BundlesDetail {
    id: number;
    sale_id: number;
    item_id: number;
    ip: string | null;
    msisdn: string | null;
    user_name: string;
    contact_id: string;
    item_name: string;
    user_uuid: string;
}