import { useState, useEffect } from 'react';
import axios from 'axios';

export interface ListEmails {
    name:  string;
    email: string;
    uuid:  string;
}

export const useGetEmails = () => {
    const [data, setData] = useState<ListEmails[] | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true); // Iniciar como true si esperas cargar datos inmediatamente
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error("Authentication token is missing");
                }
                const headers = {
                    'Authorization': `Bearer ${token}`,
                };

                // Asegúrate de que la URL es la correcta para obtener los emails
                // Cambia '/crm/sales' por la URL correcta si es necesario
                const response = await axios.get<ListEmails[]>(`${apiUrl}/crm/send-emails`, { headers });
                
                setData(response.data); // Removí el ordenamiento ya que no hay una propiedad 'date'
            } catch (error: any) {
                setError(error.response?.data?.message || error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [apiUrl]); // Agregué apiUrl como dependencia por si cambia

    return { data, isLoading, error };
};
