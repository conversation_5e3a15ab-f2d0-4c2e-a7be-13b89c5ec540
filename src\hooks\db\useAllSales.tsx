import { useState, useEffect } from 'react';
import axios from 'axios';

import { Sales } from '../../interfaces/sales';


export const useAllUserSales = () => {
    const [data, setData] = useState<Sales[] | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const headers = {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                }

                const response = await axios.get<Sales[]>(`${apiUrl}/crm/sales`, { headers });
                setData(response.data.sort((a, b) => {
                    return new Date(b.date).getTime() - new Date(a.date).getTime();
                }));
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, []);

    return { data, isLoading, error };
};