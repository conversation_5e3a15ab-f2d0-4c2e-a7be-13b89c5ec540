import { useState, useEffect } from 'react';
import axios from 'axios';


interface Email {
    Date: string;
    From: string;
    Subject: string;
    Body: string;
    id: string;
    threadId: string;
    historyId: string;
}

export const useEmailsUser = (emailId: string) => {
    const [data, setData] = useState<Email>();
    // const [data, setData] = useState<any>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const headers = {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                }

                const response = await axios.get(`${apiUrl}/crm/user/emails/${emailId}`, { headers });

                setData(response.data);
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, []);

    return { data, isLoading, error };
};