import { useState, useEffect } from 'react';
import axios from 'axios';


interface Email {
    id: string;
    subject: string;
    body: string;
}

export const useEmail = (emailId: string) => {
    const [data, setData] = useState<Email>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const headers = {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                }

                const response = await axios.get(`${apiUrl}/crm/get-email/${emailId}`, { headers });

                setData(response.data);
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, []);

    return { data, isLoading, error };
};