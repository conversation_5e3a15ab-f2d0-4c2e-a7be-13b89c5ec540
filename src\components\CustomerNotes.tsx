import React, { useEffect, useState } from 'react';
import { useUpdateCustomerNotes } from '../hooks/update/useUpdateCustomerNotes';

interface Note {
    Title: string;
    Date: string;
    Content: string;
    User: string;
}

interface NotesProps {
    notes: Note[];
    uuid: string;
    token: string;
}

const CustomerNotes: React.FC<NotesProps> = ({ notes, uuid, token }) => {
    const [localNotes, setLocalNotes] = useState(Array.isArray(notes) ? notes : []);
    const { useUpdateCustomerNotesRequest } = useUpdateCustomerNotes();

    const currentUser = localStorage.getItem('username') || 'Null';

    const addNote = () => {
        const newNote = {
            Title: 'New Note',
            Date: new Date().toISOString(),
            Content: '',
            User: localStorage.getItem('username') || 'Null'
        };

        if (!localNotes || localNotes.length === 0) {
            setLocalNotes([newNote]);
        } else {
            const newNotes = [...localNotes, newNote];
            setLocalNotes(newNotes);
        }
    };

    // const saveNote = (index: number) => {
    //     // Get the current note
    //     const note = localNotes[index];

    //     // Save the note
    //     setLocalNotes(prevNotes => prevNotes.map((prevNote, i) => i === index ? { ...prevNote, Date: new Date().toISOString() } : prevNote));

    //     // Log the notes
    //     console.log(localNotes);

    //     // print json of notes {"notes": []}
    //     console.log(JSON.stringify({ notes: localNotes }));

    // }

    const deleteNote = (index: number) => {
        // Find note with the exact index and remove it
        setLocalNotes(prevNotes => prevNotes.filter((_, i) => i !== index));
    };

    const updateNoteTitle = (index: number, newTitle: string) => {
        setLocalNotes(prevNotes => prevNotes.map((note, i) => i === index ? { ...note, Title: newTitle } : note));
    };

    const updateNoteContent = (index: number, newContent: string) => {
        setLocalNotes(prevNotes => prevNotes.map((note, i) => i === index ? { ...note, Content: newContent } : note));
    };

    useEffect(() => {
        console.log(notes);
        setLocalNotes(notes);
    }, [notes]);

    useEffect(() => {
        console.log(localNotes);
        // Sort notes based on date
        setLocalNotes(prevNotes => prevNotes.sort((a, b) => new Date(b.Date).getTime() - new Date(a.Date).getTime()));

        useUpdateCustomerNotesRequest(token, uuid, localNotes.length > 0 ? { notes: localNotes } : { notes: [] });

        console.log("TOKEN: " + token);
        console.log("UUID: " + uuid);
    }, [localNotes]);

    return (
        <div>
            {localNotes && localNotes.length > 0 ? (
                localNotes.map((note, index) => (
                    <div key={index} className="card mt-3">
                        <div className="card-header">
                            {note.User === currentUser ? (
                                <input type="text" className="form-control" value={note.Title} onChange={event => updateNoteTitle(index, event.target.value)} />
                            ) : (
                                <h5 className="card-title">{note.Title}</h5>
                            )}
                            <p className="card-subtitle text-muted small mt-2">{new Date(note.Date).toLocaleString()} - {note.User}</p>
                        </div>
                        <div className="card-body">
                            {note.User === currentUser ? (
                                <textarea className="form-control" value={note.Content} onChange={event => updateNoteContent(index, event.target.value)} />
                            ) : (
                                <p className="card-text">{note.Content}</p>
                            )}
                        </div>
                        {note.User === currentUser && (
                            <div>
                                {/* <button className="btn btn-primary mt-3" onClick={() => saveNote(index)}>Guardar nota</button> */}
                                <button className="btn btn-danger mt-3" onClick={() => deleteNote(index)}>Borrar nota</button>
                            </div>
                        )}
                    </div>
                ))
            ) : (
                <p>No notes available.</p>
            )}
            <button className="btn btn-primary mt-3" onClick={addNote}>Add Note</button>
            <button className="btn btn-secondary mt-3 ml-2" onClick={() => console.log(localNotes)}>Console log notes</button>
        </div>
    );
};

export default CustomerNotes;