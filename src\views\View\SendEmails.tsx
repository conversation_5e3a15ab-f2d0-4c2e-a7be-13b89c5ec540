import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import toast, { Toaster } from 'react-hot-toast';
import { useGetEmails } from '../../hooks/emails/useGetEmails';
import { useSendEmails } from '../../hooks/db/useSendEmails';

interface Recipient {
    name: string;
    email: string;
    uuid: string;
}

const SendEmails: React.FC = () => {
    const { data, isLoading, error } = useGetEmails();
    const { sendEmails, isLoading: isSending } = useSendEmails();
    const [hasError, setHasError] = useState(false);
    const [recipients, setRecipients] = useState<Recipient[]>([]);
    const navigate = useNavigate();
    const [filteredRecipients, setFilteredRecipients] = useState<Recipient[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [subject, setSubject] = useState('');
    const [body, setBody] = useState('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [selectedRecipients, setSelectedRecipients] = useState<Set<string>>(new Set());

    const handleAttachmentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            setAttachments(Array.from(event.target.files));
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Asegúrate de que los estados están definidos y contienen los datos correctos.
        // Estos estados deben ser definidos en tu componente con useState.

        // attachments debe ser un array de objetos File.
        // recipients debe ser un array de objetos con { name, email, uuid }.
        // selectedRecipients debe ser un Set que contiene uuids de los destinatarios seleccionados.

        const filteredRecipients = recipients.filter(recipient => selectedRecipients.has(recipient.uuid));

        // Verifica que hay al menos un destinatario seleccionado.
        if (filteredRecipients.length === 0) {
            toast.error('Debes seleccionar al menos un destinatario.');
            return;
        }

        // Ahora construye la estructura de datos para enviar.
        const emailData = {
            subject, // Este estado debe contener el asunto del email.
            body,    // Este estado debe contener el cuerpo del email.
            attachments, // Este estado debe contener los archivos adjuntos.
            destinations: filteredRecipients, // Destinatarios ya filtrados.
        };

        try {
            // Llama a la función sendEmails y espera su respuesta.
            await sendEmails(emailData);

            // Limpia los estados después de enviar el correo electrónico.
            setSubject('');
            setBody('');
            setAttachments([]);
            setSelectedRecipients(new Set());

            // Notifica al usuario que el correo se envió correctamente.
            toast.success('Correo enviado exitosamente!');
        } catch (error) {
            setHasError(true);
            // Notifica al usuario que hubo un error al enviar el correo.
            toast.error(`Error al enviar el correo: ${(error as Error).message}`);
        }
    };


    const handleSelectRecipient = (uuid: string) => {
        setSelectedRecipients((prevSelected) => {
            const newSelected = new Set(prevSelected);
            if (newSelected.has(uuid)) {
                newSelected.delete(uuid);
            } else {
                newSelected.add(uuid);
            }
            return newSelected;
        });
    };

    const handleSelectAllRecipients = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.checked) {
            setSelectedRecipients(new Set(recipients.map((r) => r.uuid)));
        } else {
            setSelectedRecipients(new Set());
        }
    };

    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSearchTerm(value);
        if (value) {
            const filtered = recipients.filter(recipient =>
                recipient.name.toLowerCase().includes(value.toLowerCase()) ||
                recipient.email.toLowerCase().includes(value.toLowerCase())
            );
            setFilteredRecipients(filtered);
        } else {
            setFilteredRecipients(recipients);
        }
    };

    useEffect(() => {
        if (searchTerm) {
            const filtered = recipients.filter(recipient =>
                recipient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                recipient.email.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredRecipients(filtered);
        } else {
            setFilteredRecipients(recipients);
        }
    }, [searchTerm, recipients]);

    useEffect(() => {
        if (data) {
            setRecipients(data);
            setFilteredRecipients(data);
        }
    }, [data]);

    if (isLoading) {
        return <div className="container-fluid p-5">Cargando...</div>;
    }

    if (error) {
        return <div className="container-fluid p-5">Error: {error}</div>;
    }

    return (
        <div className="container-fluid p-5">
            <Toaster />
            <div>
                <div className="card-body">
                    <h2 className="card-title mb-3">Enviar Correo</h2>
                    <form onSubmit={handleSubmit}>
                        {/* ponemos el texto de la cantidad de destinatarios seleccionados */}
                        <div className="mb-3">
                            <label htmlFor="recipients" className="form-label">Destinatarios</label>
                            <input
                                type="text"
                                className="form-control"
                                id="recipients"
                                value={`${selectedRecipients.size > 0
                                    ? `${selectedRecipients.size} destinatarios seleccionados`
                                    : 'Selecciona destinatarios'
                                    }`}
                                readOnly
                            />
                        </div>
                        <div className="mb-3">
                            <label htmlFor="subject" className="form-label">Asunto</label>
                            <input
                                type="text"
                                className="form-control"
                                id="subject"
                                value={subject}
                                onChange={(e) => setSubject(e.target.value)}
                            />
                        </div>
                        <div className="mb-3">
                            <label htmlFor="body" className="form-label">Cuerpo</label>
                            <textarea
                                className="form-control"
                                id="body"
                                rows={3}
                                value={body}
                                onChange={(e) => setBody(e.target.value)}
                            ></textarea>
                        </div>
                        <div className="mb-3">
                            <label htmlFor="attachments" className="form-label">Adjuntos</label>
                            <input
                                type="file"
                                className="form-control"
                                id="attachments"
                                multiple
                                onChange={handleAttachmentChange}
                            />
                        </div>
                        <button
                            type="submit"
                            className={`btn ${hasError ? 'btn-danger' : 'btn-primary'}`}
                            disabled={isSending}
                        >
                            {isSending ? 'Enviando...' : 'Enviar'}
                        </button>
                    </form>
                </div>
            </div>
            <table className="table">
                <thead>
                    <tr>
                        <th>
                            <input
                                type="checkbox"
                                onChange={handleSelectAllRecipients}
                                checked={selectedRecipients.size === recipients.length}
                            />
                        </th>
                        <th>Nombre</th>
                        <th>Email</th>
                        <th style={{ width: '10%' }} className='text-center'>
                            {/* mostramos el número de destinatarios seleccionados  y el total de destinatarios */}
                            {`${selectedRecipients.size > 0
                                ? `${selectedRecipients.size} de ${recipients.length}`
                                : `${recipients.length}`
                                }`}
                        </th>
                        <th style={{ width: '10%' }}>
                            <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Buscar destinatarios..."
                                value={searchTerm}
                                onChange={handleSearchChange}
                            />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {filteredRecipients.map((recipient) => ( // Cambia `recipients` a `filteredRecipients` aquí
                        <tr key={recipient.uuid}>
                            <td>
                                <input
                                    type="checkbox"
                                    checked={selectedRecipients.has(recipient.uuid)}
                                    onChange={() => handleSelectRecipient(recipient.uuid)}
                                />
                            </td>
                            <td onClick={() => navigate(`/customers/${recipient.uuid}`)} style={{ cursor: 'pointer' }}>{recipient.name}</td>
                            <td colSpan={3}>{recipient.email}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default SendEmails;