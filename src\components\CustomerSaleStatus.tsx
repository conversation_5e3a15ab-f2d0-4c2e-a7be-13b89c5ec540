import React, { useState, useEffect } from 'react';
import { useGetSetSaleState } from '../hooks/db/useGetSetSaleState';

interface SaleCardProps {
    sale_id: string;
}

export const CustomerSaleStatus: React.FC<SaleCardProps> = ({ sale_id }) => {
    const { getData, getIsLoading, getError, getSaleState, setIsLoading, setSaleState } = useGetSetSaleState();
    const [selectedState, setSelectedState] = useState('En proceso');

    useEffect(() => {
        const fetchSale = async () => {
            if (sale_id) {
                await getSaleState(sale_id);
            }
        };

        fetchSale();
    }, [sale_id, getSaleState]);

    useEffect(() => {
        if (!setIsLoading) {
            getSaleState(sale_id);
        }
    }, [setIsLoading, getSaleState, sale_id]);

    if (getIsLoading) {
        return <div>Cargando...</div>;
    }

    if (getError) {
        return <div>Error: {getError}</div>;
    }

    return (
        <div className="card">
            <div className="card-body">

                <h2>Estado de la venta</h2>
                {/* dropdown to add a new state */}
                <div className="input-group mb-3">
                    <select className="form-select" value={selectedState} onChange={(e) => setSelectedState(e.target.value)}>
                        <option value="Pendiente de firma">Pendiente de firma</option>
                        <option value="Completado">Completado</option>
                        <option value="Cancelado">Cancelado</option>
                    </select>
                    <label
                        className={`input-group-text ${setIsLoading ? 'loading' : ''}`}
                        htmlFor="inputGroupSelect01"
                        style={{ backgroundColor: '#198754', color: 'white' }}
                        onClick={() => {
                            if (!setIsLoading) {
                                if (window.confirm('Quieres actualizar el estado?')) {
                                    console.log('Crear estado clicked: ', sale_id, ' - ', selectedState);
                                    setSaleState(sale_id, selectedState);
                                }
                            }
                        }}
                    >
                        {setIsLoading ? 'Loading...' : 'Actualizar estado'}
                    </label>
                </div>
                {getData && getData.sales && (
                    <table className="table">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Estado</th>
                            </tr>
                        </thead>
                        <tbody>
                            {getData.sales.sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime()).map((sale: any, index: number) => (
                                <tr key={index} className={index === 0 ? 'table-primary' : ''}>
                                    <td>{sale.date}</td>
                                    <td>{sale.state}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>
        </div>
    );
}