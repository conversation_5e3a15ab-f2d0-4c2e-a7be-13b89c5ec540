import React, { useEffect } from 'react';
import { useCustomerFiles } from '../hooks/mongo/useCustomerFiles';
import { useDownloadFiles } from '../hooks/mongo/useDownloadFiles';

const CustomerFiles: React.FC<{ zendesk_id: string }> = ({ zendesk_id }) => {
    const { loading, error, data } = useCustomerFiles(zendesk_id);
    const { fetchData, loading: doc_load } = useDownloadFiles();

    useEffect(() => {
        console.log(data);
    }, [data]);

    if (loading) return <p>Loading...</p>;

    if (error) return <p>Error: {error.message}</p>;

    if (!data) return <p>No data found</p>;

    return <p>En mantenimiento...</p>

    return (
        <div className="container mt-5">
            <h1 className="mb-4">Customer Files</h1>
            <div className="row">
                {data.map((file: any, index: number) => (
                    <div key={index} className={`col-md-4 mb-3`}>
                        <div className="card">
                            <div className="card-body">
                                <p className="card-text"><strong>File ID:</strong> {file._id}</p>
                                <p className="card-text"><strong>Customer ID:</strong> {file.customer_id}</p>
                                <p className="card-text"><strong>Filename:</strong> {file.filename}</p>
                                <button className='btn btn-primary' onClick={() => fetchData(file._id, file.filename)} disabled={doc_load}>Download Files</button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};
export default CustomerFiles;
