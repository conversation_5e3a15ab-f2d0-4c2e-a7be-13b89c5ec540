import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Sale, SaleItem } from '../../interfaces/sales'; // Import interfaces
import { useSale } from '../../hooks/db/useSale';
import { useUpdateSale } from '../../hooks/update/useUpdateSale';
import { useDeleteSale } from '../../hooks/db/useDeleteSale';

import { CustomerCard } from '../../components/CustomerCard';
import { CustomerSaleStatus } from '../../components/CustomerSaleStatus';

import DownloadLink from '../../components/DownloadLink';
import DownloadLinkPortabilidad from '../../components/DownloadLinkPortabilidad';
import SaleNotes from '../../components/SaleNotes';

interface EditableSaleItem extends SaleItem {
    index: number;
    isEditing: boolean;
    originalItem: SaleItem; // Store the original item for cancel functionality
    bundleSales?: EditableSaleItem[];
    [key: string]: any; // Add index signature
}

const SaleDetails: React.FC = () => {
    const [saleDetails, setSaleDetails] = useState<Sale | null>(null);
    const { saleId } = useParams<{ saleId?: string }>();
    const { data, error } = useSale(saleId || '');
    const [editableItems, setEditableItems] = useState<EditableSaleItem[]>([]);
    const { updateSaleRequest } = useUpdateSale();
    const { deleteSale } = useDeleteSale();

    const navigate = useNavigate();

    useEffect(() => {
        if (data) {
            setSaleDetails(data);
            console.log(data);
            const editableData = data.items.map((item, idx) => ({
                ...item,
                index: idx,
                isEditing: false,
                originalItem: { ...item },
                bundleSales: item.bundleSales?.map((bundleItem, bundleIdx) => ({
                    ...bundleItem,
                    index: bundleIdx,
                    isEditing: false,
                    originalItem: { ...bundleItem }
                }))
            }));
            setEditableItems(editableData as EditableSaleItem[]);
        }

        if (error) {
            console.error(error);
        }
    }, [data, error]);

    const handleEditChange = (index: number, field: string, value: string, bundleIndex?: number) => {
        setEditableItems(items =>
            items.map(item =>
                item.index === index ? {
                    ...item,
                    [field]: bundleIndex === undefined ? value : item[field],
                    bundleSales: item.bundleSales?.map((bundleItem, bundleIdx) =>
                        bundleIdx === bundleIndex ? { ...bundleItem, [field]: value } : bundleItem
                    )
                } : item
            )
        );
    };

    const handleSave = (item: EditableSaleItem, bundleIndex?: number) => {
        if (window.confirm('Are you sure you want to save changes?')) {
            console.log('Saving item', item);
            setEditableItems(items =>
                items.map(i =>
                    i.index === item.index ? {
                        ...i,
                        isEditing: typeof bundleIndex === 'undefined' ? false : i.isEditing,
                        bundleSales: i.bundleSales?.map((bundleItem, bundleIdx) =>
                            bundleIdx === bundleIndex ? { ...bundleItem, isEditing: false } : bundleItem
                        )
                    } : i
                )
            );

            // Log the changes
            console.log('Original item', item.originalItem);
            console.log('Edited item', item);

            let sentItem = { ...item };

            // Add "type_item" property to the sent item
            sentItem.type_item = sentItem.type;

            // If a bundle item is being edited, send the bundle item as a regular item but with the type as "Bundle Item"
            if (typeof bundleIndex !== 'undefined') {
                const bundleItem = item.bundleSales?.[bundleIndex];
                if (bundleItem) {
                    sentItem = { ...bundleItem, type_item: 'Bundle Item' };
                }
            }

            updateSaleRequest(localStorage.getItem('token') as string, sentItem);
        }
    };

    const handleEdit = (index: number, bundleIndex?: number) => {
        setEditableItems(items =>
            items.map(item =>
                item.index === index ? {
                    ...item,
                    isEditing: bundleIndex === undefined ? true : item.isEditing,
                    bundleSales: item.bundleSales?.map((bundleItem, bundleIdx) =>
                        bundleIdx === bundleIndex ? { ...bundleItem, isEditing: true } : bundleItem
                    )
                } : item
            )
        );
    };

    const handleCancel = (index: number, bundleIndex?: number) => {
        setEditableItems(items =>
            items.map(item =>
                item.index === index ? {
                    ...item,
                    isEditing: bundleIndex === undefined ? false : item.isEditing,
                    bundleSales: item.bundleSales?.map((bundleItem, bundleIdx) =>
                        bundleIdx === bundleIndex ? {
                            ...bundleItem,
                            ...bundleItem.originalItem,
                            isEditing: false
                        } : bundleItem
                    ) as EditableSaleItem[] || []
                } : item
            )
        );
    };    

    if (!saleDetails) {
        return <div>Loading...</div>;
    }

    return (
        <div className='container mt-4'>

            {saleDetails.user_id ? (
                <div>
                    <CustomerCard customerId={saleDetails.user_id} />
                </div>
            ) : (
                <div>No user id available</div>
            )}
            <br></br>

            <CustomerSaleStatus sale_id={saleDetails.saleId} />

            <br></br>

            <div className='card'>
                <div className='card-header'>
                    <div className='row'>
                        <div className='col-6'>
                            <h4>Detalles de venta</h4>
                        </div>
                        <div className='col-6 d-flex align-items-center'>
                            <h4>Precio:&nbsp;</h4>
                            <h4 className='ml-2'>{saleDetails.items.reduce((acc, item) => acc + item.price, 0).toFixed(2)}€</h4>
                        </div>
                    </div>
                </div>

                <div className="card-body">

                    <table className="table">
                        <thead>
                            <tr>
                                <th>Item ID</th>
                                <th>Nombre</th>
                                <th>Precio</th>
                                <th>Tipo</th>
                                <th>IP</th>
                                <th>MSISDN</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {editableItems.map(item => (
                                <>
                                    <tr key={item.index + "" + item.sale_detail_id}>
                                        <td>{item.itemId}</td>
                                        {/* <td>{item.index + "" + item.sale_detail_id}</td> */}
                                        <td onClick={() => navigate(`/products/${item.itemId}`)} style={{ cursor: 'pointer' }}>{item.name}</td>
                                        <td>{item.price ? item.price.toFixed(2) : '0.00'}€</td>
                                        <td>{item.type}</td>
                                        <td>
                                            <input
                                                type="text"
                                                value={item.ip || ''}
                                                onChange={e => handleEditChange(item.index, 'ip', e.target.value)}
                                                disabled={!item.isEditing}
                                                className='form-control'
                                            />
                                        </td>
                                        <td>
                                            <input
                                                type="text"
                                                value={item.msisdn || ''}
                                                onChange={e => handleEditChange(item.index, 'msisdn', e.target.value)}
                                                disabled={!item.isEditing}
                                                className='form-control'
                                            />
                                        </td>
                                        <td>
                                            {!item.isEditing && (
                                                <button onClick={() => handleEdit(item.index)} className='btn btn-secondary'>
                                                    Edit
                                                </button>
                                            )}
                                            {item.isEditing && (
                                                <div className="btn-group">
                                                    <button onClick={() => handleSave(item)} className='btn btn-primary'>
                                                        Save
                                                    </button>
                                                    <button onClick={() => handleCancel(item.index)} className='btn btn-danger'>
                                                        Cancel
                                                    </button>
                                                </div>
                                            )}
                                        </td>
                                    </tr>

                                    {
                                        // If the item has bundle sales, render them
                                        item.bundleSales !== undefined && item.bundleSales.length > 0 && (
                                            item.bundleSales.map(bundleSale => (
                                                // Italic text
                                                <tr key={(item.index || 0) + "" + (bundleSale.sale_detail_id || 0)} style={{ fontStyle: 'italic' }}>
                                                    <td style={{ fontStyle: 'italic' }}>{bundleSale.itemId}</td>
                                                    {/* <td onClick={() => navigate(`/products/${bundleSale.itemId}`)} style={{ cursor: 'pointer' }}>{bundleSale.name}</td> */}
                                                    <td style={{ fontStyle: 'italic' }}>{bundleSale.name}</td>
                                                    <td style={{ fontStyle: 'italic' }}>{bundleSale.price ? bundleSale.price.toFixed(2) : 'N/A'}€</td>
                                                    <td style={{ fontStyle: 'italic' }}>{bundleSale.type}</td>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            value={bundleSale.ip || ''}
                                                            onChange={e => handleEditChange(item.index, 'ip', e.target.value, bundleSale.index)}
                                                            disabled={!bundleSale.isEditing}
                                                            className='form-control'
                                                        />
                                                    </td>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            value={bundleSale.msisdn || ''}
                                                            onChange={e => handleEditChange(item.index, 'msisdn', e.target.value, bundleSale.index)}
                                                            disabled={!bundleSale.isEditing}
                                                            className='form-control'
                                                        />
                                                    </td>

                                                    <td>
                                                        {!bundleSale.isEditing && (
                                                            <button onClick={() => handleEdit(item.index, bundleSale.index)} className='btn btn-secondary'>
                                                                Edit
                                                            </button>
                                                        )}
                                                        {bundleSale.isEditing && (
                                                            <div className="btn-group">
                                                                <button onClick={() => handleSave(item, bundleSale.index)} className='btn btn-primary'>
                                                                    Save
                                                                </button>
                                                                <button onClick={() => handleCancel(item.index, bundleSale.index)} className='btn btn-danger'>
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        )}
                                                    </td>
                                                </tr>
                                            ))
                                        )
                                    }
                                </>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            <DownloadLink saleId={saleDetails.saleId} className='mt-4' />&nbsp;
            <DownloadLinkPortabilidad saleId={saleDetails.saleId} className='mt-4' phoneNumber={saleDetails.items[0].msisdn} />

            <br></br>

            <SaleNotes token={localStorage.getItem('token') || ''} uuid={saleDetails.saleId} />

            <button
                onClick={() => {
                    if (window.confirm('Seguro que quieres eleminar esta venta?')) {
                        if (saleDetails.saleId) {
                            deleteSale(saleDetails.saleId);
                            console.log('Deleting sale', saleDetails.saleId);
                            navigate('/sales');
                        }
                    }
                }}
                className='btn btn-danger'
                style={{ position: 'fixed', bottom: '20px', right: '20px' }}
            >
                Delete Sale
            </button>
        </div>
    );
}

export default SaleDetails;