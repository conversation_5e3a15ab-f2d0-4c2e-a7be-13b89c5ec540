import React, { createContext, useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTokenInfo } from '../hooks/useTokenInfo';

interface AuthContextProps {
  isLoggedIn: boolean;
  login: (data: LoginData, error: boolean) => void;
  logout: () => void;
}

interface LoginData {
  access_token: string;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const { data, loading, error, sendTokenRequest } = useTokenInfo(token ?? '');
  const [isLoggedIn, setIsLoggedIn] = useState(!!token);
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoggedIn) {
      navigate('/login');
    }
  }, [isLoggedIn, navigate]);

  useEffect(() => {
    if (token) {
      sendTokenRequest();
    } else {
      setIsLoggedIn(false);
    }
  }, [token, sendTokenRequest]);

  useEffect(() => {
    if (data && 'UUID' in data) {
      setIsLoggedIn(true);
    } else {
      setIsLoggedIn(false);
    }
  }, [data]);

  useEffect(() => {
    if (error) {
      console.error(error);
      setIsLoggedIn(false);
    }
  }, [error]);

  const login = (loginData: LoginData, hasError: boolean) => {
    if (hasError || !loginData.access_token) {
      setIsLoggedIn(false);
    } else {
      localStorage.setItem('token', loginData.access_token);
      setToken(loginData.access_token);
      setIsLoggedIn(true);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setIsLoggedIn(false);
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="text-center">
          <div className="spinner-border text-primary m-5 p-4" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{ isLoggedIn, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextProps => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
