import React, { useState, useEffect } from 'react';
import { FaAddressBook } from "react-icons/fa";
import { Link, useNavigate } from 'react-router-dom';

import { useAllUserSales } from '../hooks/db/useAllSales';
import { Sales } from '../interfaces/sales';


const Sales: React.FC = () => {
    const navigate = useNavigate();
    const { data: sales } = useAllUserSales();

    const [searchTerm, setSearchTerm] = useState('');
    const [filteredSales, setFilteredSales] = useState<Sales[]>([]);

    const [ivaPercentage, setIvaPercentage] = useState(21);

    const handleIvaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setIvaPercentage(Number(e.target.value));
    }

    const [isHovered, setIsHovered] = useState(false);
    const buttonStyle: React.CSSProperties = {
        width: isHovered ? 'auto' : '50px',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        transition: 'max-width 0.3s ease-in-out, opacity 0.3s ease-in-out'
    };

    useEffect(() => {
        const results = searchTerm
            ? sales?.filter(sale =>
                sale.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                sale.date.toLowerCase().includes(searchTerm.toLowerCase())
            )
            : sales;

        setFilteredSales(results as Sales[]);
    }, [searchTerm, sales]);


    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    }

    return (
        <div className="container mt-4">

            <div className="row">
                <div className="col">

                    <li className="nav-item ms-auto" style={{ listStyleType: 'none' }}>
                        <div className="row">
                            <div className="col-md-8">
                                <input
                                    type="text"
                                    className="form-control mb-4"
                                    placeholder="Buscar venta..."
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                />
                            </div>
                            <div className="col-md-4">
                                <div className="input-group">
                                    <span className="input-group-text">IVA</span>
                                    <input
                                        type="number"
                                        className="form-control"
                                        placeholder="% IVA"
                                        value={ivaPercentage}
                                        onChange={handleIvaChange}
                                    />
                                </div>
                            </div>
                        </div>
                    </li>

                    <Link to="/sales/create" className="btn btn-success position-fixed bottom-0 end-0 m-3"
                        style={buttonStyle}
                        onMouseEnter={() => setIsHovered(true)}
                        onMouseLeave={() => setIsHovered(false)}
                    >
                        <FaAddressBook size={25} />
                        {isHovered && <span className="ms-2">Crear Venta</span>}
                    </Link>

                    <div className='table-responsive'>
                        <table className='table table-hover'>
                            <thead>
                                <tr>
                                    <th scope='col'>ID</th>
                                    <th scope='col'>Nombre</th>
                                    <th scope='col'>Fecha</th>
                                    <th scope='col'>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {
                                    filteredSales?.map((sale) => (
                                        <tr key={sale.sale_id}
                                            onClick={() => navigate(`/sales/${sale.sale_id}`)}
                                            style={{ cursor: 'pointer' }}>
                                            <th scope='row'>{sale.sale_id}</th>
                                            <td>{sale.first_name + ""}</td>
                                            <td>{sale.date}</td>
                                            <td>{sale.total_price == null ? "-" : (sale.total_price * (1 + ivaPercentage / 100)).toFixed(2) + " €"}</td>
                                        </tr>
                                    ))

                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Sales;