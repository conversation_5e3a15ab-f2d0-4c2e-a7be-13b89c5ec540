import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Modal from 'react-modal';

import { CustomerData } from '../../types/customer';
import { useCustomers } from '../../hooks/db/useCustomers';
import SalesTable from '../../components/SalesTable';

import { useUpdateCustomer } from '../../hooks/update/useUpdateCustomer';
import { useUserSales } from '../../hooks/db/useUserSales';

import CustomerNotes from '../../components/CustomerNotes';
import { useCustomerNotes } from '../../hooks/db/useCustomerNotes';

import CustomerInvoices from '../../components/CustomerInvoices';
import CustomerEmails from '../../components/CustomerEmails';

import { useDeleteUser } from '../../hooks/db/useDeleteUser';
import CustomerFiles from '../../components/CustomerFiles';

import { useBajaUser } from '../../hooks/user/useBajaUser';

interface Note {
    Title: string;
    Date: string;
    Content: string;
    User: string;
}

Modal.setAppElement('#root'); // Modify this to match your app's root element

const CustomerDetails: React.FC = () => {
    // import navigate
    const navigate = useNavigate();

    const { darBajaUser } = useBajaUser();

    const [token] = useState<string | null>(localStorage.getItem('token'));
    const { data, sendSpecificCustomerRequest } = useCustomers();
    const [customerDetails, setCustomerDetails] = useState<CustomerData | null>(null);
    const { customerId } = useParams<{ customerId?: string }>();
    const { d2 } = useUserSales(customerId || '');
    const { updateCustomerRequest } = useUpdateCustomer();

    const [editing, setEditing] = useState(false);
    const [editableCustomerDetails, setEditableCustomerDetails] = useState<CustomerData | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { deleteUser } = useDeleteUser();

    const [confirmationsNeeded, setConfirmationsNeeded] = useState(0);
    const [isDeleteClicked, setIsDeleteClicked] = useState(false);
    const handleDeleteClick = () => {
        setIsDeleteClicked(true);
        setConfirmationsNeeded(3);
        setButtonStyle({
            position: 'absolute',
            backgroundColor: 'green',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
        });
    };

    const [buttonStyle, setButtonStyle] = useState({});
    const handleConfirmClick = () => {
        const randomTop = Math.floor(Math.random() * 80); // Generate a random top value
        const randomLeft = Math.floor(Math.random() * 80); // Generate a random left value

        setButtonStyle({
            position: 'absolute',
            top: `${randomTop}%`,
            left: `${randomLeft}%`,
            backgroundColor: 'green',
            color: 'white',
            padding: '10px 20px',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
        });

        setConfirmationsNeeded(confirmationsNeeded - 1);
        if (confirmationsNeeded <= 1) {
            // Call the function to delete the contact here
            console.log('Contact deleted');
            deleteUser(customerId || '');
            navigate('/customers');
        }
    };

    const { getCustomerNotesRequest } = useCustomerNotes();
    const [dataNotes, setDataNotes] = useState<{ notes: Note[] } | null>({
        "notes": [
        ]
    });

    const [navItem, setNavItem] = useState("details");


    useEffect(() => {
        const fetchCustomerNotes = async () => {
            if (customerId) {
                const response = await getCustomerNotesRequest(token || '', customerId);

                console.log(response);

                if (response !== null && response !== undefined) {
                    const parsedResponse = JSON.parse(response);
                    setDataNotes(parsedResponse);
                    console.log(dataNotes);
                    console.log(typeof dataNotes);
                }
            }
        };

        fetchCustomerNotes();
    }, [customerId, token]);



    useEffect(() => {
        const fetchCustomer = async () => {
            if (customerId) {
                await sendSpecificCustomerRequest(customerId);
            }
        };

        fetchCustomer();
    }, [customerId, sendSpecificCustomerRequest]);

    useEffect(() => {
        if (data && data.length > 0) {
            const initialCustomerData = data[0];
            setCustomerDetails(initialCustomerData);
            setEditableCustomerDetails({ ...initialCustomerData });
        }
    }, [data]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setEditableCustomerDetails(prev => prev ? { ...prev, [name]: value } : null);
    };

    const handleEdit = () => {
        setEditing(true);
    };

    const handleSave = async () => {
        setIsModalOpen(true);
    };

    const confirmSave = () => {
        setIsModalOpen(false);
        setEditing(false);
        setCustomerDetails(editableCustomerDetails);

        // Console log for testing
        console.log(editableCustomerDetails);

        if (token) {
            updateCustomerRequest(token, editableCustomerDetails);
        }
    }

    const handleCancel = () => {
        setEditing(false);
        setIsModalOpen(false);
        setEditableCustomerDetails({
            id: customerDetails?.id || 0,
            first_name: customerDetails?.first_name || '',
            contact_id: customerDetails?.contact_id || '',
            national_id: customerDetails?.national_id || '',
            mobile_phone: customerDetails?.mobile_phone || '',
            email: customerDetails?.email || '',
            address: customerDetails?.address || '',
            postal_code: customerDetails?.postal_code || '',
            city: customerDetails?.city || '',
            province: customerDetails?.province || '',
            bank_account: customerDetails?.bank_account || '',
            billing_address: customerDetails?.billing_address || '',
            billing_postal_code: customerDetails?.billing_postal_code || '',
            billing_city: customerDetails?.billing_city || '',
            billing_province: customerDetails?.billing_province || '',
            shipping_address: customerDetails?.shipping_address || '',
            shipping_postal_code: customerDetails?.shipping_postal_code || '',
            shipping_city: customerDetails?.shipping_city || '',
            shipping_province: customerDetails?.shipping_province || '',
            status: customerDetails?.status || 0,
        });
    };


    if (!customerDetails) {
        return <div className="text-center p-5"><div className="spinner-border text-primary" role="status"><span className="sr-only">Loading...</span></div></div>;
    }


    return (
        <div className="container mt-4">
            <ul className="nav nav-tabs">
                <li className="nav-item">
                    <button className={`nav-link ${navItem === 'details' ? 'active' : ''}`} onClick={() => setNavItem('details')}>Detalles</button>
                </li>
                <li className="nav-item">
                    <button className={`nav-link ${navItem === 'invoices' ? 'active' : ''}`} onClick={() => setNavItem("invoices")}>Facturas</button>
                </li>
                <li className="nav-item">
                    <button className={`nav-link ${navItem === 'sales' ? 'active' : ''}`} onClick={() => setNavItem("sales")}>Ventas</button>
                </li>
                <li className="nav-item">
                    <button className={`nav-link ${navItem === 'notes' ? 'active' : ''}`} onClick={() => setNavItem("notes")}>Notas</button>
                </li>
                <li className='nav-item'>
                    <button className={`nav-link ${navItem === 'emails' ? 'active' : ''}`} onClick={() => setNavItem("emails")}>Emails</button>
                </li>
                <li className='nav-item'>
                    <button className={`nav-link ${navItem === 'files' ? 'active' : ''}`} onClick={() => setNavItem("files")}>Archivos</button>
                </li>
                <li className='nav-item'>
                    <button className={`nav-link ${navItem === 'actions' ? 'active' : ''}`} onClick={() => setNavItem("actions")}>Acciones</button>
                </li>
            </ul>

            <div className='tab-content'>
                <div className={`tab-pane fade mt-2 ${navItem === 'details' ? 'show active' : ''}`}>
                    <h2 className="text-center my-4">Detalles del Cliente</h2>
                    {
                        editing ? (
                            <>
                                <div className="row">
                                    <div className="col-md-6">
                                        <div className="card mb-4">
                                            <div className="card-body">
                                                <h2 className="card-title">Datos Personales</h2>
                                                <div className="form-group">
                                                    <label htmlFor="first_name">Nombre</label>
                                                    <input type="text" className="form-control" id="first_name" name="first_name" value={editableCustomerDetails?.first_name || ''} onChange={handleInputChange} />

                                                    <label htmlFor="mobile_phone">Teléfono</label>
                                                    <input type="text" className="form-control" id="mobile_phone" name="mobile_phone" value={editableCustomerDetails?.mobile_phone || ''} onChange={handleInputChange} />

                                                    <label htmlFor="email">Email</label>
                                                    <input type="text" className="form-control" id="email" name="email" value={editableCustomerDetails?.email || ''} onChange={handleInputChange} />

                                                    <label htmlFor="national_id">DNI</label>
                                                    <input type="text" className="form-control" id="national_id" name="national_id" value={editableCustomerDetails?.national_id || ''} onChange={handleInputChange} />
                                                </div>
                                            </div>
                                        </div>

                                        <div className="card mb-4">
                                            <div className='card-body'>
                                                <h2 className="card-title">Datos de envio</h2>

                                                <div className="form-group">
                                                    <label htmlFor="shipping_address">Dirección</label>
                                                    <input type="text" className="form-control" id="shipping_address" name="shipping_address" value={editableCustomerDetails?.shipping_address || ''} onChange={handleInputChange} />

                                                    <label htmlFor="shipping_postal_code">Código postal</label>
                                                    <input type="text" className="form-control" id="shipping_postal_code" name="shipping_postal_code" value={editableCustomerDetails?.shipping_postal_code || ''} onChange={handleInputChange} />

                                                    <label htmlFor="shipping_city">Población</label>
                                                    <input type="text" className="form-control" id="shipping_city" name="shipping_city" value={editableCustomerDetails?.shipping_city || ''} onChange={handleInputChange} />

                                                    <label htmlFor="shipping_province">Provincia</label>
                                                    <input type="text" className="form-control" id="shipping_province" name="shipping_province" value={editableCustomerDetails?.shipping_province || ''} onChange={handleInputChange} />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-md-6">

                                        <div className="card mb-4">
                                            <div className="card-body">
                                                <h2 className="card-title">Dirección</h2>
                                                <div className="form-group">
                                                    <label htmlFor="address">Dirección</label>
                                                    <input type="text" className="form-control" id="address" name="address" value={editableCustomerDetails?.address || ''} onChange={handleInputChange} />

                                                    <label htmlFor="postal_code">Código postal</label>
                                                    <input type="text" className="form-control" id="postal_code" name="postal_code" value={editableCustomerDetails?.postal_code || ''} onChange={handleInputChange} />

                                                    <label htmlFor="city">Población</label>
                                                    <input type="text" className="form-control" id="city" name="city" value={editableCustomerDetails?.city || ''} onChange={handleInputChange} />

                                                    <label htmlFor="province">Provincia</label>
                                                    <input type="text" className="form-control" id="province" name="province" value={editableCustomerDetails?.province || ''} onChange={handleInputChange} />
                                                </div>
                                            </div>
                                        </div>

                                        <div className="card mb-4">
                                            <div className="card-body">
                                                <h2 className="card-title">Datos Bancarios</h2>
                                                <div className="form-group">
                                                    <label htmlFor="bank_account">Cuenta bancaria</label>
                                                    <input type="text" className="form-control" id="bank_account" name="bank_account" value={editableCustomerDetails?.bank_account || ''} onChange={handleInputChange} />

                                                    <label htmlFor="billing_address">Dirección</label>
                                                    <input type="text" className="form-control" id="billing_address" name="billing_address" value={editableCustomerDetails?.billing_address || ''} onChange={handleInputChange} />

                                                    <label htmlFor="billing_postal_code">Código postal</label>
                                                    <input type="text" className="form-control" id="billing_postal_code" name="billing_postal_code" value={editableCustomerDetails?.billing_postal_code || ''} onChange={handleInputChange} />

                                                    <label htmlFor="billing_city">Población</label>
                                                    <input type="text" className="form-control" id="billing_city" name="billing_city" value={editableCustomerDetails?.billing_city || ''} onChange={handleInputChange} />

                                                    <label htmlFor="billing_province">Provincia</label>
                                                    <input type="text" className="form-control" id="billing_province" name="billing_province" value={editableCustomerDetails?.billing_province || ''} onChange={handleInputChange} />


                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="button-container">
                                        <button onClick={handleSave} className="btn btn-success d-inline-block mr-2">Save</button>
                                        <button onClick={handleCancel} className="btn btn-danger d-inline-block">Cancel</button>
                                    </div>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="row">
                                    <div className="col-md-6">
                                        <div className="card mb-4">
                                            <div className="card-body">
                                                <h2 className="card-title">Datos Personales</h2>
                                                <p><strong>Nombre:</strong> {customerDetails.first_name}</p>
                                                <p><strong>Teléfono:</strong> {customerDetails.mobile_phone}</p>
                                                <p><strong>Email:</strong> {customerDetails.email}</p>
                                                <p><strong>DNI:</strong> {customerDetails.national_id}</p>
                                            </div>
                                        </div>
                                        <div className="card">
                                            <div className="card-body">
                                                <h2 className="card-title">Datos de envío</h2>
                                                <p><strong>Dirección de envío:</strong> {customerDetails.shipping_address}</p>
                                                <p><strong>Código postal de envío:</strong> {customerDetails.shipping_postal_code}</p>
                                                <p><strong>Población de envío:</strong> {customerDetails.shipping_city}</p>
                                                <p><strong>Provincia de envío:</strong> {customerDetails.shipping_province}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="card mb-4">
                                            <div className="card-body">
                                                <h2 className="card-title">Dirección</h2>
                                                <p><strong>Dirección:</strong> {customerDetails.address}</p>
                                                <p><strong>Código postal:</strong> {customerDetails.postal_code}</p>
                                                <p><strong>Población:</strong> {customerDetails.city}</p>
                                                <p><strong>Provincia:</strong> {customerDetails.province}</p>
                                            </div>
                                        </div>
                                        <div className="card">
                                            <div className="card-body">
                                                <h2 className="card-title">Datos Bancarios</h2>
                                                <p><strong>Cuenta bancaria:</strong> {customerDetails.bank_account.replace(/.(?=.{4})/g, '*')}</p>
                                                <p><strong>Dirección de facturación:</strong> {customerDetails.billing_address}</p>
                                                <p><strong>Código postal de facturación:</strong> {customerDetails.billing_postal_code}</p>
                                                <p><strong>Población de facturación:</strong> {customerDetails.billing_city}</p>
                                                <p><strong>Provincia de facturación:</strong> {customerDetails.billing_province}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button onClick={handleEdit} className="btn btn-primary d-inline-block">Edit</button>
                            </>
                        )
                    }

                    <Modal
                        isOpen={isModalOpen}
                        onRequestClose={handleCancel}
                        contentLabel="Edit Customer Details"
                    >

                        <h2>Confirmar cambios</h2>
                        <p>¿Estás seguro de que quieres guardar los cambios?</p>

                        <p><strong>Nombre:</strong> {editableCustomerDetails?.first_name}</p>
                        <p><strong>Teléfono:</strong> {editableCustomerDetails?.mobile_phone}</p>
                        <p><strong>Email:</strong> {editableCustomerDetails?.email}</p>
                        <p><strong>DNI:</strong> {editableCustomerDetails?.national_id}</p>
                        <p><strong>Dirección:</strong> {editableCustomerDetails?.address}</p>
                        <p><strong>Código postal:</strong> {editableCustomerDetails?.postal_code}</p>
                        <p><strong>Población:</strong> {editableCustomerDetails?.city}</p>
                        <p><strong>Provincia:</strong> {editableCustomerDetails?.province}</p>
                        <p><strong>Cuenta bancaria:</strong> {editableCustomerDetails?.bank_account.replace(/.(?=.{4})/g, '*')}</p>
                        <p><strong>Dirección de facturación:</strong> {editableCustomerDetails?.billing_address}</p>
                        <p><strong>Código postal de facturación:</strong> {editableCustomerDetails?.billing_postal_code}</p>
                        <p><strong>Población de facturación:</strong> {editableCustomerDetails?.billing_city}</p>
                        <p><strong>Provincia de facturación:</strong> {editableCustomerDetails?.billing_province}</p>
                        <p><strong>Dirección de envío:</strong> {editableCustomerDetails?.shipping_address}</p>
                        <p><strong>Código postal de envío:</strong> {editableCustomerDetails?.shipping_postal_code}</p>
                        <p><strong>Población de envío:</strong> {editableCustomerDetails?.shipping_city}</p>
                        <p><strong>Provincia de envío:</strong> {editableCustomerDetails?.shipping_province}</p>


                        <button onClick={confirmSave}>Save</button>
                        <button onClick={handleCancel}>Cancel</button>
                    </Modal>
                </div>
                <div className={`tab-pane fade mt-2 ${navItem === 'invoices' ? 'show active' : ''}`}>
                    <h2 className="text-center my-4">Facturas</h2>
                    <CustomerInvoices userId={customerDetails.contact_id} />
                </div>
                <div className={`tab-pane fade mt-2 ${navItem === 'sales' ? 'show active' : ''}`}>
                    <h2 className="text-center my-4">Ventas</h2>
                    <SalesTable sales={d2} />
                </div>
                <div className={`tab-pane fade mt-2 ${navItem === 'notes' ? 'show active' : ''}`}>
                    <h2 className="text-center my-4">Notas:</h2>
                    {dataNotes && <CustomerNotes notes={dataNotes.notes} uuid={customerId || ''} token={token || ''} />}
                </div>
                <div className={`tab-pane fade mt-2 ${navItem === 'emails' ? 'show active' : ''}`}>
                    <CustomerEmails customer_uuid={customerId || ''} />
                </div>
                <div className={`tab-pane fade mt-2 ${navItem === 'files' ? 'show active' : ''}`}>
                    <CustomerFiles zendesk_id={customerDetails.contact_id || ''} />
                </div>
                <div className={`tab-pane fade mt-2 ${navItem === 'actions' ? 'show active' : ''}`} style={{ backgroundColor: '#f8f9fa', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                    <h2 className="text-center my-4" style={{ color: 'red' }}>Borrar Contacto</h2>
                    {!isDeleteClicked && (
                        <button onClick={handleDeleteClick} style={{ backgroundColor: 'red', color: 'white', padding: '10px 20px', border: 'none', borderRadius: '5px', cursor: 'pointer' }}>Borrar el contacto</button>
                    )}
                    {confirmationsNeeded > 0 && (
                        <div style={{ marginTop: '20px', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                            <p>Clicame 3 veces al botón para borrar el contacto.</p>
                            <button onClick={handleConfirmClick} style={buttonStyle}>Clicame {confirmationsNeeded} vece(s) más</button>
                        </div>
                    )}

                    <hr />

                    <h2 className="text-center my-4">Dar de baja</h2>
                    <button className="btn btn-danger" onClick={() => {
                        if (window.confirm('Seguro que quieres dar de baja a este cliente?')) {
                            // if (window.confirm('Si das de baja este cliente todos sus productos se darán de baja también. ¿Estás seguro?')) {
                            console.log('Dar de baja');
                            darBajaUser(token || '', customerId || '');
                            // }
                        }
                    }}>Dar de baja</button>
                </div>
            </div>
        </div>
    );
}

export default CustomerDetails;