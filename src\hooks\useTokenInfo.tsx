import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

interface TokenInfoData {
    UUID?: string;
    username?: string;
}

export const useTokenInfo = (token: string | null) => {
    const [data, setData] = useState<TokenInfoData | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const apiUrl = import.meta.env.VITE_API_URL;

    const sendTokenRequest = useCallback(async () => {
        if (!token) {
            setError(new Error('No token provided') as AxiosError);
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const headers = {
                'Authorization': `Bearer ${token}`
            };

            const response = await axios.get<TokenInfoData>(`${apiUrl}/users/me`, { headers });

            setData(response.data);

            // if response.data.username exist then console.log it
            if (response.data.username) {
                localStorage.setItem('username', response.data.username);
            }
        } catch (err) {
            setError(err as AxiosError);
        } finally {
            setLoading(false);
        }
    }, [token, apiUrl]); // Dependencias: token y apiUrl

    return { data, loading, error, sendTokenRequest };
};
