import React from 'react';
import { useInvoiceUser } from '../hooks/invoice/useInvoiceUser';

interface InvoiceProps {
    userId: string;
}

interface Invoice {
    name: string;
    id: string;
    date: string;
    url: string;
}

const CustomerInvoices: React.FC<InvoiceProps> = ({ userId }) => {
    const { data, isLoading, error } = useInvoiceUser(userId);

    const apiUrl = import.meta.env.VITE_API_URL;

    const generateLink = (invoice: Invoice) => {
      const invoiceDate = invoice.name.split("#")[1].slice(-6);
      let invoiceID = invoice.id;
      const invoiceLink = `${apiUrl}/invoice?id=${invoiceID}&date=${invoiceDate}`;
      return invoiceLink;
    }
  
    const downloadAndOpenPDF = (invoice: Invoice) => {
      const link = document.createElement('a');
      link.href = generateLink(invoice);
      link.download = 'invoice.pdf'; // This will trigger the download
      link.target = '_blank'; // This will open the PDF in a new tab after downloading
      link.click(); // This will click the link programmatically
    }

    if (isLoading) {
        return <div>Loading...</div>;
    }

    if (error) {
        return <div>Error: {error}</div>;
    }

    if (!data || (data.message && data.message === "No objects found for the given user_id.")) {
        return <div>No data available</div>;
    }

    return (
        <div className="container mt-5">
            {data
                .sort((a: Invoice, b: Invoice) => {
                    const dateA = new Date(a.date);
                    const dateB = new Date(b.date);
                    return dateB.getTime() - dateA.getTime();
                })
                .map((invoice: Invoice, index: number) => (
                    <div key={index} className="card mb-3">
                        <div className="card-body">
                            <h3 className="card-title">{invoice.name}</h3>
                            <p className="card-text">ID: {invoice.id}</p>
                            <p className="card-text">Fecha: {invoice.date}</p>
                            <button
                                className="btn btn-primary"
                                onClick={() => downloadAndOpenPDF(invoice)}
                            >
                                Descargar factura
                            </button>
                        </div>
                    </div>
                ))}

                {/* Show json */}
                <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
    );
};

export default CustomerInvoices;