import React, { useState, FormEvent, useEffect } from 'react';
import { useProducts } from '../../hooks/db/useProducts';
import { CustomerData } from '../../types/customer';
import { useCustomers } from '../../hooks/db/useCustomers';
import { useSaleCreate } from '../../hooks/creates/useSaleCreate';
import { useNavigate } from 'react-router-dom';
import Select from 'react-select';


interface SaleData {
    uuid: string;
    items: string[];
    bundles: string[];
}

interface Product {
    id: number;
    name: string;
    price?: number;
    provider: string;
    product_id: string;
}

interface Bundle {
    id: number;
    name: string;
    total_price: number;
}

const CreateSale: React.FC = () => {
    const navigate = useNavigate();

    const [formData, setFormData] = useState<SaleData>({
        uuid: '',
        items: [],
        bundles: [],
    });

    const [token] = useState<string | null>(localStorage.getItem('token'));

    const { data, sendProductRequest } = useProducts(token as string);
    const { data: customers, sendCustomerRequest } = useCustomers();

    const [sortedCustomers, setSortedCustomers] = useState<CustomerData[]>([]);

    const { sendSaleRequest } = useSaleCreate();


    useEffect(() => {
        if (token) {
            sendProductRequest();
        }
    }, [token, sendProductRequest]);

    useEffect(() => {
        console.log("Loading customers...");
        sendCustomerRequest();
    }, []);

    useEffect(() => {
        const sortedCustomers = customers.sort((a, b) => {
            if (a.first_name < b.first_name) return -1;
            if (a.first_name > b.first_name) return 1;
            return 0;
        });
        console.log("Customers loaded!");
        console.log(sortedCustomers);
        console.log(customers);
        setSortedCustomers(sortedCustomers);
    }, [customers]);


    // Find the most similar product or bundle based on the ID

    const findSimilarProductOrBundle = (id: string, list: Product[] | Bundle[]) => {
        console.log("List: ", list);
        return list.find(item => item.id.toString() === id.toString()) || '';
        // Do a for loop and find matching ID
        // for (let i = 0; i < list.length; i++) {
        //     // console.log(list[i].id.toString())
        //     if (list[i].id.toString() === id.toString()) {
        //         console.log("Found similar product: ", list[i]);
        //         return list[i];
        //     }
        // }
    };

    const handleCustomerChange = (selectedOption: any) => {
        const option = selectedOption as { value: string; label: string; };
        setFormData({ ...formData, uuid: option.value });
    };

    const handleItemChange = (selectedOption: any, index: number) => {
        const newItems = [...formData.items];
        const similarProduct = findSimilarProductOrBundle(selectedOption.value, data?.products || []);
        newItems[index] = similarProduct ? similarProduct.id.toString() : 'q';

        setFormData({ ...formData, items: newItems });
    };

    const handleBundleChange = (selectedOption: any, index: number) => {
        const newBundles = [...formData.bundles];
        const similarBundle = findSimilarProductOrBundle(selectedOption.value, data?.bundles || []);
        newBundles[index] = similarBundle ? similarBundle.id.toString() : '';
        setFormData({ ...formData, bundles: newBundles });
    };

    const addItem = () => {
        setFormData({ ...formData, items: [...formData.items, ''] });
    };

    const addBundle = () => {
        setFormData({ ...formData, bundles: [...formData.bundles, ''] });
    };

    const removeItem = (index: number) => {
        const filteredItems = formData.items.filter((_, idx) => idx !== index);
        setFormData({ ...formData, items: filteredItems });
    };

    const removeBundle = (index: number) => {
        const filteredBundles = formData.bundles.filter((_, idx) => idx !== index);
        setFormData({ ...formData, bundles: filteredBundles });
    };

    const options = sortedCustomers?.map((customer: CustomerData) => ({
        value: customer.id,
        label: `${customer.first_name} - ${customer.email}`,
    }));

    const renderCustomerDropdown = () => (
        <div className="mb-3">
            <label htmlFor="customer" className="form-label">Select Customer</label>
            <Select
                className="basic-single"
                classNamePrefix="select"
                defaultValue={options[0]}
                isSearchable
                name="color"
                options={options}
                onChange={handleCustomerChange}
            />
        </div>
    );

    const renderItems = () => (
        formData.items.map((id, index) => {
            const options = data?.products.map(product => ({
                value: product.id,
                label: `${product.name} - ${(product as any).price ? `${(product as any).price}` + '€' : 'Price not available'}`
            }));

            return (
                <div className="mb-3" key={index}>
                    <label htmlFor={`item-${index}`} className="form-label">Item</label>
                    <div className="input-group w-100">
                        <Select
                            className="basic-single w-75"
                            classNamePrefix="select"
                            defaultValue={options?.find(option => option.value === Number(id))}
                            isSearchable
                            name={`item-${index}`}
                            options={options}
                            onChange={(selectedOption) => handleItemChange(selectedOption, index)}
                        />
                        <button className="btn btn-danger w-25" type="button" onClick={() => removeItem(index)}>Remove</button>
                    </div>
                </div>
            );
        })
    );

    const renderBundles = () => (
        formData.bundles.map((id, index) => {
            const options = data?.bundles.map(bundle => ({
                value: bundle.id,
                label: `${bundle.name} - ${bundle.total_price}`
            }));

            return (
                <div className="mb-3" key={index}>
                    <label htmlFor={`bundle-${index}`} className="form-label">Bundle</label>
                    <div className="input-group">
                        <Select
                            className="basic-single w-75"
                            classNamePrefix="select"
                            defaultValue={options?.find(option => option.value === Number(id))}
                            isSearchable
                            name={`bundle-${index}`}
                            options={options}
                            onChange={(selectedOption) => handleBundleChange(selectedOption, index)}
                        />
                        <button className="btn btn-danger w-25" type="button" onClick={() => removeBundle(index)}>Remove</button>
                    </div>
                </div>
            );
        })
    );


    const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        // Remove empty items and bundles
        const filteredItems = formData.items.filter(id => id !== '');
        const filteredBundles = formData.bundles.filter(id => id !== '');
        const newFormData = { ...formData, items: filteredItems, bundles: filteredBundles };
        console.log(newFormData);

        if (window.confirm('Are you sure you want to submit the form?')) {
            try {
                sendSaleRequest(token as string, newFormData);
                navigate('/sales');
            } catch (error) {
                console.log(error);
            }
        }
    };

    return (
        <div className="container mt-4 py-5">
            <form onSubmit={handleSubmit}>
                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Customer Info</h2>
                        {renderCustomerDropdown()}
                    </div>
                </div>

                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Productos</h2>
                        <div className="row">
                            {renderItems()}
                            <button type="button" onClick={addItem} className="btn btn-primary">Add Item</button>
                        </div>
                    </div>
                </div>

                <div className="card mb-4">
                    <div className="card-body">
                        <h2 className="card-title">Bundles</h2>
                        <div className="row">
                            {renderBundles()}
                            <button type="button" onClick={addBundle} className="btn btn-primary">Add Bundle</button>
                        </div>
                    </div>
                </div>

                <button type="submit" className="btn btn-primary">Create Sale</button>
            </form>
        </div>
    );
};

export default CreateSale;