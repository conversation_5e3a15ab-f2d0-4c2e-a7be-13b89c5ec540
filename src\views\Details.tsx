import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { useSalesDetails } from '../hooks/db/useSalesDetails';
import { useUpdateDetails } from '../hooks/update/useUpdateDetails';

import { FaCheck } from "react-icons/fa";
import { ImCancelCircle } from "react-icons/im";
import { MdPointOfSale } from "react-icons/md";

interface Detail {
  id: number;
  sale_id: number;
  item_id: number;
  ip: string | null;
  msisdn: string | null;
  user_name: string;
  contact_id: string;
  item_name: string;
  user_uuid: string;
  type_item: 'Sale' | 'Bundle Item';
  isEditing?: boolean;  // Used for toggling edit mode in the UI
}

const Details: React.FC = () => {
  const { data: detailsData, isLoading, error } = useSalesDetails();
  const { updateSaleEditRequest } = useUpdateDetails();
  const [editableDetails, setEditableDetails] = useState<Detail[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    setEditableDetails(detailsData?.map((detail: Detail) => ({ ...detail, isEditing: false })) || []);
  }, [detailsData]);

  const handleEditChange = (index: number, field: keyof Detail, value: string) => {
    setEditableDetails(currentDetails =>
      currentDetails.map((detail, idx) =>
        idx === index ? { ...detail, [field]: value } : detail
      )
    );
  };

  const handleSave = async (index: number) => {
    if (window.confirm('¿Estás seguro de que deseas guardar los cambios?')) {
      const formData = new FormData();
      formData.append('sale_detail_id', editableDetails[index].id.toString());
      formData.append('ip', editableDetails[index].ip || '');
      formData.append('msisdn', editableDetails[index].msisdn || '');
      formData.append('type_item', editableDetails[index].type_item);
      updateSaleEditRequest(localStorage.getItem('token') ?? '', formData);

      // delete de table row
      setEditableDetails(currentDetails =>
        currentDetails.filter((_, idx) => idx !== index)
      );
    }
  };

  const handleEdit = (index: number) => {
    setEditableDetails(currentDetails =>
      currentDetails.map((detail, idx) =>
        idx === index ? { ...detail, isEditing: true } : detail
      )
    );
  };

  const handleCancel = (index: number) => {
    setEditableDetails(currentDetails =>
      currentDetails.map((detail, idx) =>
        idx === index ? { ...detail, isEditing: false } : detail
      )
    );
  };

  const renderDetailRow = (detail: Detail, index: number) => (
    <tr key={detail.id}>
      <td onClick={() => navigate(`/customers/${detail.user_uuid}`)} style={{ cursor: 'pointer' }}>{detail.user_name}</td>
      <td>{detail.contact_id}</td>
      <td>{detail.item_name}</td>
      <td>{
        detail.isEditing ? (
          <input
            type="text"
            value={detail.ip || ''}
            onChange={(e) => handleEditChange(index, 'ip', e.target.value)}
            className='form-control'
          />
        ) : detail.ip
      }</td>
      <td>{
        detail.isEditing ? (
          <input
            type="text"
            value={detail.msisdn || ''}
            onChange={(e) => handleEditChange(index, 'msisdn', e.target.value)}
            className='form-control'
          />
        ) : detail.msisdn
      }</td>
      <td>{
        detail.isEditing ? (
          <div className="btn-group">
            <button onClick={() => handleSave(index)} className="btn btn-success">
              <FaCheck size={15} />
            </button>
            <button onClick={() => handleCancel(index)} className="btn btn-danger">
              <ImCancelCircle size={15} />
            </button>
          </div>
        ) : (
          <div className="btn-group">
            <button onClick={() => handleEdit(index)} className="btn btn-warning">Editar</button>
            <button onClick={() => navigate(`/sales/${detail.sale_id}`)} className="btn btn-primary">
              <MdPointOfSale size={15} />
            </button>
          </div>
        )
      }</td>
    </tr>
  );

  return (
    <div className="container mt-4 py-5">
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
      <div className="alert alert-primary" role="alert">
        <h4 className="alert-heading">Información de la Tabla</h4>
        <p>Esta tabla muestra los productos que aún no tienen asignada una dirección IP o un número de teléfono (MSISDN).</p>
        <hr />
        <h4 className="alert-heading">¿Cómo Editar la Información?</h4>
        <p>Sigue estos pasos para actualizar la información de un producto:</p>
        <ul>
          <li><strong>Paso 1:</strong> Localiza el producto que deseas editar y haz clic en el botón <strong>Editar</strong>.</li>
          <li><strong>Paso 2:</strong> Escribe los nuevos datos en los campos que aparecen.</li>
          <li><strong>Paso 3:</strong> Presiona <strong>Guardar</strong> para confirmar los cambios y actualizar la información en la base de datos.</li>
          <li>Si decides no cambiar nada, simplemente haz clic en <strong>Cancelar</strong> para deshacer los cambios.</li>
        </ul>
      </div>
      <div className="table-responsive">
        <table className="table table-hover table-striped">
          <thead>
            <tr>
              <th>Nombre de Usuario</th>
              <th>ID de Contacto</th>
              <th>Nombre del ítem</th>
              <th>IP</th>
              <th>MSISDN</th>
              <th>Acciones</th>
            </tr>
          </thead>
          <tbody>
            {editableDetails.map(renderDetailRow)}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Details;
