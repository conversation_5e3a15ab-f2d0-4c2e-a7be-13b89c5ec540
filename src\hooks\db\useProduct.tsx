import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';
import qs from 'qs'; 

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  recurring: boolean;
  recurrence_period: number;
  speed: number;
  data_bonus: number;
  sms_count: number;
  voice_calls: number;
  product_type: string;
  line_type: string;
  provider: string;
  status: boolean;
  is_filter: boolean;
}

interface User {
  uuid: string;
  name: string;
  email: string;
}

interface ProductsData {
  product: Product;
  users: User[];
}

interface UseProductsReturnType {
  data: ProductsData | null;
  loading: boolean;
  error: AxiosError | null;
  sendProductRequest: () => Promise<void>;
}

export const useProduct = (token: string, product_id: string): UseProductsReturnType => {
  const [data, setData] = useState<ProductsData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);
  const apiUrl = import.meta.env.VITE_API_URL as string;

  const sendProductRequest = useCallback(async () => {
    if (!token) {
      setError(new Error('No token provided') as AxiosError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const formData = qs.stringify({ product_id });
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      const response = await axios.post<ProductsData>(`${apiUrl}/crm/product`, formData, { headers });
      setData(response.data);
    } catch (err) {
      setError(err as AxiosError)
    } finally {
      setLoading(false);
    }
  }, [token, apiUrl, product_id]);

  return { data, loading, error, sendProductRequest };
};