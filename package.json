{"name": "gatchan-crm", "private": true, "version": "0.0.0", "type": "module", "scripts": {"hard-reload": "rmdir /S /Q node_modules\\.vite && npm install && npm run dev", "dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.2", "bootstrap": "^5.3.2", "dompurify": "^3.0.8", "iban": "^0.0.14", "jspdf": "^2.5.1", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-modal": "^3.16.1", "react-router-dom": "^6.19.0", "react-select": "^5.8.0", "toast": "^0.5.4"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/iban": "^0.0.35", "@types/qs": "^6.9.10", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-modal": "^3.16.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}}