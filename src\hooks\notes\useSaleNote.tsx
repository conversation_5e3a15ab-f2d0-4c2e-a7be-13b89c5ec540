import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

interface Note {
    Title: string;
    Date: string;
    Content: string;
    User: string;
}

export const useSaleNote = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const [data, setData] = useState<Note[] | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL;

    const useUpdateSaleNotes = useCallback(async (token: string, uuid: string, formData: any) => {
        setLoading(true);
        setError(null);
        setData(null);

        console.log(uuid);

        try {
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            };

            const response = await axios.post(`${apiUrl}/crm/sale/notes/update`, formData, { headers });

            // setData(response.data);
            console.log(response.data);
        } catch (error: unknown) {
            setError(error as AxiosError<unknown, any>);
        } finally {
            setLoading(false);
        }
    }, []);

    const fetchData = async (id: string) => {
        setLoading(true);
        setError(null);
        setData(null);

        try {
            let token = localStorage.getItem('token');
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            };

            const response = await axios.get(`${apiUrl}/crm/sale/notes/${id}`, { headers });
            setData(response.data.notes);
            console.log(response.data.notes);
        } catch (error: unknown) {
            setError(error as AxiosError<unknown, any>);
        } finally {
            setLoading(false);
        }
    }

    return { loading, error, data, useUpdateSaleNotes, fetchData };
}