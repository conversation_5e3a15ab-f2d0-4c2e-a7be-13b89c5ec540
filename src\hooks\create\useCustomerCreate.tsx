import { useState } from 'react';
import axios from 'axios';

export const useCustomerCreate = () => {
    const [data, setData] = useState<any>();
    const apiUrl = import.meta.env.VITE_API_URL;

    const token = localStorage.getItem('token');

    const sendCustomerCreateRequest = async (formData: any): Promise<void> => {
        try {
            console.log(formData)
            const response = await axios.post(`${apiUrl}/crm/customer/create`, formData, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response) {
                throw new Error('Error: Empty response');
            }

            const responseData: any = response.data;
            console.log('Query result:', responseData);

            setData(responseData);

        } catch (error) {
            if (axios.isAxiosError(error)) {
                console.error('Error fetching customers:', error.response?.status);
            } else {
                console.error('Error fetching customers:', error);
            }
        }
    };

    return { data, sendCustomerCreateRequest };
};