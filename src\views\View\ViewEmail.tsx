import React, { useEffect, useState } from 'react';
import { useEmail } from '../../hooks/emails/useEmail';
import { useParams } from 'react-router-dom';

interface Email {
    id: string;
    subject: string;
    body: string;
}

const ViewEmail: React.FC = () => {
    const { emailId } = useParams<{ emailId?: string}>();
    const emailData = emailId ? useEmail(emailId) : undefined;
    const data = emailData ? emailData.data : undefined;

    const [email, setEmail] = useState<Email | null>(null);

    useEffect(() => {
        console.log(emailId);
    }, [emailId]);

    useEffect(() => {
        console.log(data);
        if (data) {
            setEmail(data);
        }
    }, [data]);

    return (
        <div className="container">
            <h1 className="my-3">View Email: {emailId}</h1>

            <div className="card my-3">
                <div className="card-header">
                    <h5 className="mb-1">Subject: {email?.subject}</h5>
                </div>
                <div className="card-body">
                    <h6 className="card-subtitle mb-2 text-muted">ID: {email?.id}</h6>
                    <p className="card-text"><strong>Body:</strong> {email?.body}</p>
                </div>
            </div>

            <pre>{JSON.stringify(data)}</pre>
        </div>
    );
}

export default ViewEmail;