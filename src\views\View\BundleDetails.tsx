import React from 'react';
// import React, { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { useParams } from 'react-router-dom';
// import { useProduct } from '../../hooks/db/useProduct';
// import { useUpdateProduct } from '../../hooks/update/useUpdateProduct';
// import Modal from 'react-modal';

// // Interfaces
// interface Product {
//     id: number;
//     name: string;
//     description: string;
//     price_with_vat: number;
//     price: number;
//     recurring: boolean;
//     recurrence_period: number;
//     speed: number;
//     data_bonus: number;
//     sms_count: number;
//     voice_calls: number;
//     product_type: string;
//     line_type: string;
//     provider: string;
//     status: boolean;
//     is_filter: boolean;
// }

// interface User {
//     uuid: string;
//     name: string;
//     email: string;
// }

const BundleDetails: React.FC = () => {
    return (
        <div></div>
    );
}

// const BundleDetails: React.FC = () => {
//     const [token] = useState<string | null>(localStorage.getItem('token'));
//     const { productId } = useParams<{ productId: string }>();
//     const [productDetails, setProductDetails] = useState<Product | null>(null);
//     const [users, setUsers] = useState<User[]>([]);
//     const { data, loading, error, sendProductRequest } = useProduct(token || '', productId || '');
//     const navigate = useNavigate();

//     const [isEditing, setIsEditing] = useState(false);
//     const [editableProductDetails, setEditableProductDetails] = useState<Product | null>(null);
//     const { updateProductRequest } = useUpdateProduct();
//     const [isModalOpen, setIsModalOpen] = useState(false);

//     useEffect(() => {
//         if (productId && token) {
//             sendProductRequest();
//         }
//     }, [productId, token, sendProductRequest]);

//     useEffect(() => {
//         setEditableProductDetails(productDetails);
//     }, [productDetails]);

//     useEffect(() => {
//         if (data) {
//             setProductDetails(data.product || null);
//             setUsers(data.users);

//             // Remove duplicate users
//             const uniqueUsers = data.users.filter((user, index, self) =>
//                 index === self.findIndex((u) => (
//                     u.uuid === user.uuid
//                 ))
//             );

//             setUsers(uniqueUsers);
//         }
//     }, [data]);

//     if (loading) {
//         return <div className="text-center p-5"><div className="spinner-border text-primary" role="status"><span className="sr-only">Loading...</span></div></div>;
//     }

//     if (error) {
//         return <div className="alert alert-danger" role="alert">Error: {error.message}</div>;
//     }

//     if (!productDetails) {
//         return <div>No product details available.</div>;
//     }

//     const handleEdit = () => {
//         setIsEditing(true);
//     };


//     const handleSave = () => {
//         setIsModalOpen(true);
//     };

//     // Handle Save button click
//     const confirmSave = () => {
//         setIsEditing(false);
//         console.log(JSON.stringify(editableProductDetails, null, 2));

//         updateProductRequest(token || '', editableProductDetails || {});

//         // Set net values of editableProductDetails to productDetails
//         if (editableProductDetails) {
//             setProductDetails({
//                 ...productDetails,
//                 name: editableProductDetails.name,
//                 description: editableProductDetails.description,
//                 price_with_vat: editableProductDetails.price_with_vat,
//                 price: editableProductDetails.price,
//                 recurrence_period: editableProductDetails.recurrence_period,
//                 speed: editableProductDetails.speed,
//                 data_bonus: editableProductDetails.data_bonus,
//                 sms_count: editableProductDetails.sms_count,
//                 voice_calls: editableProductDetails.voice_calls,
//                 product_type: editableProductDetails.product_type,
//                 line_type: editableProductDetails.line_type,
//                 provider: editableProductDetails.provider,
//                 status: editableProductDetails.status,
//                 is_filter: editableProductDetails.is_filter,
//             });

//             setIsModalOpen(false);
//         }
//     };

//     // Handle Discard button click
//     const handleDiscard = () => {
//         setEditableProductDetails(productDetails);
//         setIsEditing(false);
//     };

//     return (
//         <div className="container mt-4 py-5">

//             {
//                 isEditing ? (
//                     <>
//                         <input type="text" className="form-control mb-4" id="name" placeholder="Nombre" value={editableProductDetails?.name} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, name: e.target.value })} />
//                     </>
//                 ) : (
//                     <>
//                         <h1 className="text-center mb-4">{productDetails.name}</h1>
//                     </>
//                 )
//             }

//             <div className="row">
//                 <div className="col-md-6">

//                     {
//                         isEditing ? (
//                             <>
//                                 <div className="position-fixed bottom-0 end-0 m-3" style={{ backgroundColor: '#f8f9fa', padding: '10px', zIndex: 9999 }}>
//                                     <button className="btn btn-warning mr-2"
//                                         onClick={handleSave}>
//                                         Guardar
//                                     </button>
//                                     &nbsp;
//                                     <button
//                                         className="btn btn-danger"
//                                         onClick={handleDiscard}>
//                                         Descartar
//                                     </button>
//                                 </div>
//                             </>
//                         ) : (
//                             <>
//                                 <button className="btn btn-primary position-fixed bottom-0 end-0 m-3"
//                                     onClick={handleEdit}>
//                                     Editar
//                                 </button>
//                             </>
//                         )
//                     }

//                     <div className="card">
//                         <div className="card-header">Detalles del producto</div>
//                         <div className="card-body">
//                             <div className="form-group row">
//                                 <div className="form-group row">
//                                     <label className="col-sm-4 col-form-label"><strong>Descripción:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="text" className="form-control" id="description" placeholder="Descripción" value={editableProductDetails?.description} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, description: e.target.value })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.description}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Precio:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="number" className="form-control" id="price" placeholder="Precio" value={editableProductDetails?.price} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, price: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.price}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Precio con IVA:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="number" className="form-control" id="price_with_vat" placeholder="Precio con IVA" value={editableProductDetails?.price_with_vat} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, price_with_vat: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.price_with_vat}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Periodo de recurrencia:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="number" className="form-control" id="recurrence_period" placeholder="Periodo de recurrencia" value={editableProductDetails?.recurrence_period} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, recurrence_period: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.recurrence_period}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Velocidad:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="text" className="form-control" id="speed" placeholder="Velocidad" value={editableProductDetails?.speed} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, speed: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.speed}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Bono de datos:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="text" className="form-control" id="data_bonus" placeholder="Bono de datos" value={editableProductDetails?.data_bonus} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, data_bonus: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.data_bonus}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Cantidad de SMS:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="text" className="form-control" id="sms_count" placeholder="Cantidad de SMS" value={editableProductDetails?.sms_count} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, sms_count: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.sms_count}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Llamadas de voz:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="text" className="form-control" id="voice_calls" placeholder="Llamadas de voz" value={editableProductDetails?.voice_calls} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, voice_calls: parseInt(e.target.value) })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.voice_calls}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Tipo de producto:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <input type="text" className="form-control" id="product_type" placeholder="Tipo de producto" value={editableProductDetails?.product_type} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, product_type: e.target.value })} />
//                                         ) : (
//                                             <p className="text-muted">{productDetails.product_type}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Tipo de línea:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <select className="form-control" id="line_type" value={editableProductDetails?.line_type} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, line_type: e.target.value })}>
//                                                 <option value="mobile">Móvil</option>
//                                                 <option value="landline">Fijo</option>
//                                             </select>
//                                         ) : (
//                                             <p className="text-muted">{productDetails.line_type}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Proveedor:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <select className="form-control" id="provider" value={editableProductDetails?.provider} onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, provider: e.target.value })}>
//                                                 <option value="vodafone">Gatchan</option>
//                                             </select>
//                                         ) : (
//                                             <p className="text-muted">{productDetails.provider}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Estado:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <select
//                                                 className="form-control"
//                                                 id="status"
//                                                 value={editableProductDetails?.status ? 'true' : 'false'}
//                                                 onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, status: e.target.value === 'true' })}
//                                             >
//                                                 <option value="true">Activo</option>
//                                                 <option value="false">Inactivo</option>
//                                             </select>
//                                         ) : (
//                                             <p className="text-muted">{productDetails.status ? '🟢 Activo' : '🔴 Inactivo'}</p>
//                                         )}
//                                     </div>
//                                     <label className="col-sm-4 col-form-label"><strong>Es filtro:</strong></label>
//                                     <div className="col-sm-8">
//                                         {isEditing ? (
//                                             <select
//                                                 className="form-control"
//                                                 id="is_filter"
//                                                 value={editableProductDetails?.is_filter ? 'true' : 'false'}
//                                                 onChange={(e) => editableProductDetails && setEditableProductDetails({ ...editableProductDetails, is_filter: e.target.value === 'true' })}
//                                             >
//                                                 <option value="true">Sí</option>
//                                                 <option value="false">No</option>
//                                             </select>

//                                         ) : (
//                                             <p className="text-muted">{productDetails.is_filter ? 'Sí' : 'No'}</p>
//                                         )}
//                                     </div>

//                                 </div>
//                             </div>
//                         </div>
//                     </div>
//                 </div>
//                 <div className="col-md-6">
//                     {users.length > 0 && (
//                         <div className="card">
//                             <div className="card-header">Usuarios que tienen este producto</div>
//                             <div>
//                                 {users.map(user => (
//                                     <div key={user.uuid} className="text-decoration-none text-dark card" onClick={() => navigate(`/customers/${user.uuid}`)} style={{ cursor: 'pointer' }}>
//                                         <div className="card-body">
//                                             <p className="card-text">
//                                                 <b>{user.name}</b> ({user.email})
//                                             </p>
//                                         </div>
//                                     </div>
//                                 ))}
//                             </div>
//                         </div>
//                     )}
//                 </div>
//             </div>

//             <Modal
//                 isOpen={isModalOpen}
//                 onRequestClose={() => setIsModalOpen(false)}
//                 contentLabel="Revisar Información del Producto"
//                 className=""
//             >
//                 <div className="modal-content">
//                     <div className="modal-header">
//                         <h2 className="modal-title">Revisar Información del Producto</h2>
//                         <button type="button" className="close" onClick={() => setIsModalOpen(false)}>
//                             <span>&times;</span>
//                         </button>
//                     </div>
//                     <div className="modal-body">
//                         <p>Are you sure you want to save these changes?</p>
//                         {Object.entries(editableProductDetails || {}).map(([key, value]) => (
//                             <p key={key}>{`${key}: ${value}`}</p>
//                         ))}
//                     </div>
//                     <div className=" text-center">
//                         <button type="button" className="btn btn-primary" onClick={confirmSave}>Yes, Save</button>
//                         <button type="button" className="btn btn-secondary" onClick={() => setIsModalOpen(false)}>Cancel</button>
//                     </div>
//                 </div>
//             </Modal>

//         </div>
//     );
// };

export default BundleDetails;