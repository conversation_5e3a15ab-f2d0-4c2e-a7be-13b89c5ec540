import { useState, useEffect } from 'react';
import axios from 'axios';

export const useSalesDetails = (): { data: any, isLoading: boolean, error: string | null } => {
    const [data, setData] = useState<any | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const headers = {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                };

                // Make sure your API is expected to return an array of SalesDetails or null
                const response = await axios.post<any | null>(`${apiUrl}/crm/list-sales-details`, { headers });
                setData(response.data);
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, []);

    return { data, isLoading, error };
};
