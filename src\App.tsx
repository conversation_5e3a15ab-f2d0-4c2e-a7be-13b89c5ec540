import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import AuthProvider from './context/AuthProvider';
import Navbar from './components/navbar';

import Root from './views/Root';
import Login from './views/Auth/Login';
import Customers from './views/Customers';
import CreateCustomer from './views/Create/CreateCustomer';
import CustomerDetails from './views/View/CustomerDetails';

import ProductDetails from './views/View/ProductDetails';
import CreateProduct from './views/Create/CreateProduct';
import Product from './views/Product';
import Sales from './views/Sales';
import SaleDetails from './views/View/SaleDetails';
import CreateSale from './views/Create/CreateSale';
import Details from './views/Details';
import BundleDetails from './views/View/BundleDetails';
import SendEmails from './views/View/SendEmails';
import ViewEmails from './views/View/ViewEmails';
import ViewEmail from './views/View/ViewEmail';

function App() {
  return (
    <>
      <Router>
        <AuthProvider>
          <Routes>
            <Route path="/login" element={<Login />} />
            {/* <Route path="/db" element={<DbQuery />} /> */}
            <Route path="/dashboard" element={
              <>
                <Navbar />
                <Root />
              </>
            } />
            <Route path="/customers" element={
              <>
                <Navbar />
                <Customers />
              </>
            } />
            <Route path="/customers/:customerId" element={
              <>
                <Navbar />
                <CustomerDetails />
              </>
            } />
            <Route path="/customers/create" element={
              <>
                <Navbar />
                <CreateCustomer />
              </>
            } />

            <Route path="/products" element={
              <>
                <Navbar />
                <Product />
              </>
            } />
            <Route path="/products/:productId" element={
              <>
                <Navbar />
                <ProductDetails />
              </>
            } />
            <Route path="/bundles/:bundleId" element={
              <>
                <Navbar />
                <BundleDetails />
              </>
            } />
            <Route path="/products/create" element={
              <>
                <Navbar />
                <CreateProduct />
              </>
            } />

            <Route path="/sales" element={
              <>
                <Navbar />
                <Sales />
              </>
            } />
            <Route path="/sales/:saleId" element={
              <>
                <Navbar />
                <SaleDetails />
              </>
            } />
            <Route path="/sales/create" element={
              <>
                <Navbar />
                <CreateSale />
              </>
            } />
            <Route path="/details" element={
              <>
                <Navbar />
                <Details />
              </>
            } />
            <Route path="/emails/send" element={
              <>
                <Navbar />
                <SendEmails />
              </>
            } />
            <Route path="/emails/view" element={
              <>
                <Navbar />
                <ViewEmails />
              </>
            } />

            <Route path='/emails/view/:emailId' element={
              <>
                <Navbar/>
                <ViewEmail/>
              </>
            } />
          </Routes>
        </AuthProvider>
      </Router>
    </>
  );
}


export default App
