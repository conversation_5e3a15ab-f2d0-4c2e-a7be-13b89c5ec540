import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

export const useCustomerNotes = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const [data, setData] = useState<any | null>(null);

  const apiUrl = import.meta.env.VITE_API_URL;

    const getCustomerNotesRequest = useCallback(async (token: string, uuid: any) => {
        setLoading(true);
        setError(null);
        setData(null);

        try {
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
            };

            const response = await axios.get(`${apiUrl}/crm/customer/notes/${uuid}`, { headers });

            setData(response.data);
            // console.log(response.data);
            return response.data;
        } catch (error: unknown) {
            setError(error as AxiosError<unknown, any>);
        } finally {
            setLoading(false);
        }
    }, []);

    return { loading, error, data, getCustomerNotesRequest };
}