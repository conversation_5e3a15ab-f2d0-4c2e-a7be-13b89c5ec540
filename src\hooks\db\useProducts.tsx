import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

interface Product {
  id: number;
  name: string;
  price: number;
  provider: string;
  product_id: string;
  status: number;
}

interface Bundle {
  id: number;
  name: string;
  total_price: number;
}

interface ProductsData {
  products: Product[];
  bundles: Bundle[];
}

// Define the type for the hook's return value
interface UseProductsReturnType {
  data: ProductsData | null;
  loading: boolean;
  error: AxiosError | null;
  sendProductRequest: () => Promise<void>; // Specify that this is an async function
}

export const useProducts = (token: string): UseProductsReturnType => {
  const [data, setData] = useState<ProductsData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);
  
  // Assuming apiUrl is a constant, otherwise, you need to manage its changes
  const apiUrl = import.meta.env.VITE_API_URL as string;

  const sendProductRequest = useCallback(async () => {
    if (!token) {
      setError(new Error('No token provided') as AxiosError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      const response = await axios.get<ProductsData>(`${apiUrl}/crm/products`, { headers });
      setData(response.data);
    } catch (err) {
      setError(err as AxiosError);
    } finally {
      setLoading(false);
    }
  }, [token, apiUrl]);

  return { data, loading, error, sendProductRequest };
};
