import React, { useEffect, useState } from 'react';

interface DownloadLinkProps {
    saleId: string;
    className?: string;
    phoneNumber: string;
}

const DownloadLinkPortabilidad: React.FC<DownloadLinkProps> = ({ saleId, className = "", phoneNumber }) => {
    const [data, setData] = useState<string | undefined>('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const apiUrl = import.meta.env.VITE_API_URL as string;
    // const apiUrl = "http://localhost:8000";

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const response = await fetch(`${apiUrl}/crm/generate-sale/${saleId}/${phoneNumber}`);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const result = await response.blob();
                const url = URL.createObjectURL(result);
                setData(url);
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [saleId]);

    if (isLoading) {
        return <div className={`${className}`}><div className="spinner-border text-primary" role="status"><span className="visually-hidden">Loading...</span></div></div>;
    }

    if (error) {
        return <div className={`alert alert-danger ${className}`} role="alert">Error: {error}</div>;
    }

    return (
        <button 
            className={`btn btn-primary ${className}`}
            onClick={() => {
                const link = document.createElement('a');
                link.href = data ?? '';
                link.download = `sale_${saleId}.pdf`;
                link.click();
            }}
        >
            Descargar Venta Portabilidad
        </button>
    );
};

export default DownloadLinkPortabilidad;
