import { useState, useEffect } from 'react';


export const useInvoiceUser = (userId: string) => {
    const [data, setData] = useState<any>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;
    const token = localStorage.getItem('token');


    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const response = await fetch(`${apiUrl}/get_s3_objects/${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const result = await response.json();

                setData(result);
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
                console.log(data)
            }
        };

        fetchData();
    }, [userId]);

    return { data, isLoading, error };
};