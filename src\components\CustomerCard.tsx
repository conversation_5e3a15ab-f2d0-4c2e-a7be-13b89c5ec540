import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { CustomerData } from '../types/customer';
import { useCustomers } from '../hooks/db/useCustomers';

interface CustomerCardProps {
    customerId: string;
}

export const CustomerCard: React.FC<CustomerCardProps> = ({ customerId }) => {
    const [customerDetails, setCustomerDetails] = useState<CustomerData | null>(null);
    const { data, sendSpecificCustomerRequest } = useCustomers();

    useEffect(() => {
        const fetchCustomer = async () => {
            if (customerId) {
                await sendSpecificCustomerRequest(customerId);
            }
        };

        fetchCustomer();
    }, [customerId, sendSpecificCustomerRequest]);

    useEffect(() => {
        if (data && data.length > 0) {
            setCustomerDetails(data[0]);
        }
    }, [data]);

    return (
        <Link to={`/customers/${customerId}`} className='text-decoration-none'>
            <div className="card">
                <div className="card-body">
                    <h2 className="card-title">Datos Personales</h2>
                    <p><strong>Nombre:</strong> {customerDetails?.first_name}</p>
                    <p><strong>Teléfono:</strong> {customerDetails?.mobile_phone}</p>
                    <p><strong>Email:</strong> {customerDetails?.email}</p>
                </div>
            </div>
        </Link>
    );
}