import React, { useState, useEffect } from 'react';
import { FaAddressBook } from "react-icons/fa";
import { Link, useNavigate } from 'react-router-dom';

import { CustomerData } from '../types/customer';
import { useCustomers } from '../hooks/db/useCustomers';

// Extend to interface customer
interface Customer {
  [key: string]: string | number;
}


const Customers: React.FC = () => {
  const navigate = useNavigate();
  const { data: customers, sendCustomerRequest } = useCustomers();

  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerData[]>([]);

  const [sortConfig, setSortConfig] = useState({ key: 'contact_id', direction: 'ascending' });
  const handleSort = (key: string) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };
  useEffect(() => {
    let sortableItems = [...customers];
    if (sortConfig.key !== '') {
      sortableItems.sort((a: Customer, b: Customer) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    setFilteredCustomers(sortableItems);
  }, [searchTerm, customers, sortConfig]);

  const getSortDirectionIndicator = (key: string) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? '↑' : '↓';
    }
    return '';
  };

  const [isHovered, setIsHovered] = useState(false);
  const buttonStyle: React.CSSProperties = {
    width: isHovered ? 'auto' : '50px',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    transition: 'max-width 0.3s ease-in-out, opacity 0.3s ease-in-out'
  };

  useEffect(() => {
    sendCustomerRequest();
  }, []);


  useEffect(() => {
    const results = searchTerm
      ? customers.filter(customer =>
        customer.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.mobile_phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.national_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (customer.contact_id && customer.contact_id.toString().toLowerCase().includes(searchTerm.toLowerCase()))
      )
      : customers;

    setFilteredCustomers(results);
  }, [searchTerm, customers]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };


  return (
    <div className="container mt-4">
      <div className="row">
        <div className="col">
          <input
            type="text"
            className="form-control mb-4"
            placeholder="Buscar contactos..."
            value={searchTerm}
            onChange={handleSearchChange}
          />

          <Link to="/customers/create" className="btn btn-success position-fixed bottom-0 end-0 m-3"
            style={buttonStyle}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <FaAddressBook size={25} />
            {isHovered && <span className="ms-2">Crear Contacto</span>}
          </Link>
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th style={{ cursor: "pointer" }} onClick={() => handleSort('status')}>
                    Estado {getSortDirectionIndicator('status')}
                  </th>
                  <th style={{ cursor: "pointer" }} onClick={() => handleSort('contact_id')}>
                    ID {getSortDirectionIndicator('contact_id')}
                  </th>
                  <th style={{ cursor: "pointer" }} onClick={() => handleSort('first_name')}>
                    Nombre {getSortDirectionIndicator('first_name')}
                  </th>
                  <th style={{ cursor: "pointer" }} onClick={() => handleSort('mobile_phone')}>
                    Telefono {getSortDirectionIndicator('mobile_phone')}
                  </th>
                  <th style={{ cursor: "pointer" }} onClick={() => handleSort('national_id')}>
                    DNI {getSortDirectionIndicator('national_id')}
                  </th>
                  <th style={{ cursor: "pointer" }} className="d-none d-md-table-cell" onClick={() => handleSort('email')}>
                    Email {getSortDirectionIndicator('email')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredCustomers.length > 0 ? (
                  filteredCustomers.map(customer => (
                    <tr
                      key={customer.id}
                      onClick={() => navigate(`/customers/${customer.id}`)}
                      style={{ cursor: 'pointer' }}
                    >
                      <th scope="row">
                        <p>{customer.status === 1 ? '🟢' : '🔴'}</p>
                      </th>
                      <th scope="row" onClick={() => navigator.clipboard.writeText(customer.contact_id.toString())}>
                        {customer.contact_id}
                      </th>
                      <td>{customer.first_name}</td>
                      <td>{customer.mobile_phone}</td>
                      <td>{customer.national_id}</td>
                      <td className="d-none d-md-table-cell">{customer.email}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5}>
                      {
                        customers.length === 0 ? <p>Cargando...</p> : <p>No hay resultados</p>
                      }
                    </td>
                  </tr>
                )}
              </tbody>

            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Customers;