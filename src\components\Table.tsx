import React from "react";

interface TableProps {
    headers: string[];
    data: string[][];
    className?: string;
}

const Table: React.FC<TableProps> = ({ headers, data, className }) => {
    return (
        <div className="table-responsive">
            <table className={`table table-striped table-hover ${className}`}>
                <thead className="thead-dark">
                    <tr>
                        {headers.map((header) => (
                            <th key={header}>{header}</th>
                        ))}
                    </tr>
                </thead>
                <tbody className="table-light">
                    {data.map((row) => (
                        <tr key={row[0]}>
                            {row.map((cell) => (
                                <td key={cell}>{cell}</td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default Table;