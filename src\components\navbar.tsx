import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthProvider';

import { IoMenu } from "react-icons/io5";

const Navbar = () => {
  const [isNavCollapsed, setIsNavCollapsed] = useState(true);
  const location = useLocation();
  const { logout } = useAuth();

  const handleNavCollapse = () => setIsNavCollapsed(!isNavCollapsed);

  const navLinks = [
    { path: '/customers', text: 'Contactos' },
    { path: '/products', text: 'Catalogo' },
    { path: '/sales', text: 'Ventas' },
    { path: '/details', text: 'IPs & MSISDN' },
    // { path: '/db', text: 'DB Query',}
  ];

  useEffect(() => {
    document.title = "Gatchan | " + location.pathname.replace("/", "");
  }, [location]);

  const getNavLinkClass = (path: any) => {
    return location.pathname === path
      ? "nav-link text-white"
      : "nav-link text-secondary opacity-75";
  };

  return (
    <nav className="navbar navbar-expand-lg bg-dark">
      <div className="container-fluid">
        <Link className="navbar-brand text-white" to="/dashboard">Gatchan</Link>
        <button className="navbar-toggler border-0" type="button" onClick={handleNavCollapse} aria-controls="navbarResponsive" aria-expanded={!isNavCollapsed} aria-label="Toggle navigation">
          <IoMenu className="text-white" size={24} />
        </button>

        <div className={`${isNavCollapsed ? 'collapse' : ''} navbar-collapse`} id="navbarResponsive">
          <ul className="navbar-nav me-auto">
            {navLinks.map((link) => (
              <li key={link.text} className="nav-item">
                <Link className={getNavLinkClass(link.path)} to={link.path}>
                  {link.text}
                </Link>
              </li>
            ))}
          </ul>
          <ul className="navbar-nav ms-auto">
            <li className="nav-item">
              <button onClick={logout} className="nav-link btn btn-danger btn-link text-white">
                Logout
              </button>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
