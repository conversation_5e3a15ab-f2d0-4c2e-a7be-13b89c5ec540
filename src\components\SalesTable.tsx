import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { useItemState } from '../hooks/items/useItemState';
import { useNavigate } from 'react-router-dom';

interface SalesTableProps {
    sales: any;
}

const SalesTable: React.FC<SalesTableProps> = ({ sales }) => {
    const [modalIsOpen, setModalIsOpen] = useState(false);
    const [selectedSale, setSelectedSale] = useState<any>(null);
    const [selectedDate, setSelectedDate] = useState('');
    const [activationDate, setActivationDate] = useState('');

    const navigate = useNavigate();

    const { postData, updateActivationDate, updateItemStatus } = useItemState();

    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() < 9 ? `0${now.getMonth() + 1}` : (now.getMonth() + 1);
    const date = now.getDate() < 10 ? `0${now.getDate()}` : now.getDate();
    const formattedDate = `${year}-${month}-${date}`;

    const dissableSale = async () => {
        if (!window.confirm('Seguro que quieres desactivar este elemento?')) {
            return;
        }

        const data = {
            uid: selectedSale?.uid,
            date: selectedDate,
            type: selectedSale?.type,
        }

        await postData(data);

        setModalIsOpen(false);

        // Refresh the page
        // window.location.reload();
    }

    const updateActivationDateHandler = async () => {
        if (!window.confirm('Seguro que quieres actualizar la fecha de alta?')) {
            return;
        }

        const data = {
            uid: selectedSale?.uid,
            activation_date: activationDate,
            type: selectedSale?.type,
        }

        await updateActivationDate(data);

        setModalIsOpen(false);

        // Refresh the page
        // window.location.reload();
    }

    const toggleItemStatus = async () => {
        const newStatus = selectedSale?.sale_detail_status === 1 ? 0 : 1;
        const action = newStatus === 1 ? 'activar' : 'desactivar';

        if (!window.confirm(`Seguro que quieres ${action} este elemento?`)) {
            return;
        }

        // If changing to inactive, require a deactivation date
        if (newStatus === 0 && (!selectedDate || selectedDate === '')) {
            alert('Debe seleccionar una fecha de baja para desactivar el elemento');
            return;
        }

        const data = {
            uid: selectedSale?.uid,
            status: newStatus,
            deactivation_date: newStatus === 0 ? selectedDate : null,
            type: selectedSale?.type,
        }

        await updateItemStatus(data);

        setModalIsOpen(false);

        // Refresh the page
        // window.location.reload();
    }

    useEffect(() => {
        if (!sales)
            return;

        console.log('Sales: ', sales);

        // Sort sales by sale_id and from biggest to smallest
        sales.sort((a: any, b: any) => {
            if (a.sale_id < b.sale_id) {
                return 1;
            }
            if (a.sale_id > b.sale_id) {
                return -1;
            }
            return 0;
        });
    }, [sales]);

    // Check if sales is defined
    if (!sales) {
        return null; // or return a loading indicator or some other placeholder
    }

    // Group sales by sale_id
    const groupedSales = sales.reduce((grouped: any, sale: any) => {
        (grouped[sale.sale_id] = grouped[sale.sale_id] || []).push(sale);
        // reverse the array to show the latest sales first
        grouped[sale.sale_id].reverse();
        return grouped;
    }, {});


    return (
        <div>
            {Object.entries(groupedSales)
                .reverse().map(([saleId, items]: [string, unknown], index: number) => (<div key={index}>
                    <h2
                        onClick={() => navigate(`/sales/${saleId}`)}
                        className=""
                    ><u>
                            Venta: {saleId}
                        </u></h2>
                    <table className="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nombre</th>
                                <th>Línea</th>
                                <th>Precio</th>
                                <th>Estado</th>
                                <th>Fecha de baja</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            {(items as any[]).map((item: any, itemIndex: number) => (
                                <tr key={itemIndex}>
                                    <td>{item.item_id}</td>
                                    <td>{item.item_name}</td>
                                    <td>{item.msisdn}</td>
                                    <td>{item.item_price}</td>
                                    <td>{item.sale_detail_status === 1 ? 'Activo' : 'Inactivo'}</td>
                                    <td>
                                        {item.deactivation !== "N/A" ? new Date(item.deactivation).toISOString().split('T')[0] : item.deactivation}
                                    </td>
                                    <td>
                                        <button
                                            className="btn btn-primary"
                                            onClick={() => {
                                                setSelectedSale(item);
                                                setModalIsOpen(true);
                                                setSelectedDate(item.deactivation !== "N/A" ? new Date(item.deactivation).toISOString().split('T')[0] : formattedDate);
                                                // Set activation date from the API data
                                                setActivationDate(item.date_activation ? new Date(item.date_activation).toISOString().split('T')[0] : formattedDate);
                                            }}
                                        >
                                            Gestionar
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                ))}

            <Modal
                isOpen={modalIsOpen}
                onRequestClose={() => setModalIsOpen(false)}
                style={{
                    overlay: {
                        backgroundColor: 'rgba(0,0,0,0.5)'
                    },
                    content: {
                        width: '700px',
                        height: '500px',
                        margin: 'auto'
                    }
                }}
                contentLabel="Gestionar Elemento"
            >
                <div style={{ display: 'flex', flexDirection: 'column', height: '100%', gap: '20px' }}>
                    {/* Header with item info */}
                    <div style={{ display: 'flex', justifyContent: 'space-between', borderBottom: '1px solid #ccc', paddingBottom: '10px' }}>
                        <div>
                            <strong>ID:</strong> {selectedSale?.uid}
                        </div>
                        <div>
                            <strong>Nombre:</strong> {selectedSale?.item_name}
                        </div>
                        <div>
                            <strong>Precio:</strong> {selectedSale?.item_price}
                        </div>
                        <div>
                            <strong>Estado:</strong> {selectedSale?.sale_detail_status === 1 ? 'Activo' : 'Inactivo'}
                        </div>
                    </div>

                    {/* Current dates display */}
                    <div style={{ display: 'flex', justifyContent: 'space-between', backgroundColor: '#f8f9fa', padding: '10px', borderRadius: '4px' }}>
                        <div>
                            <strong>Fecha de Alta Actual:</strong> {selectedSale?.date ? new Date(selectedSale.date).toISOString().split('T')[0] : 'No definida'}
                        </div>
                        <div>
                            <strong>Fecha de Baja Actual:</strong> {selectedSale?.deactivation && selectedSale.deactivation !== "N/A" ? new Date(selectedSale.deactivation).toISOString().split('T')[0] : 'No definida'}
                        </div>
                    </div>

                    {/* Fecha de Alta Section */}
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                        <h5>Fecha de Alta</h5>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <input
                                type="date"
                                value={activationDate}
                                onChange={(e) => setActivationDate(e.target.value)}
                                style={{ border: '1px solid #ccc', borderRadius: '4px', padding: '6px 12px' }}
                            />
                            <button
                                className="btn btn-success"
                                onClick={updateActivationDateHandler}
                            >
                                Actualizar fecha de alta
                            </button>
                        </div>
                    </div>

                    {/* Fecha de Baja Section */}
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                        <h5>Fecha de Baja</h5>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <input
                                type="date"
                                value={selectedDate}
                                onChange={(e) => setSelectedDate(e.target.value)}
                                style={{ border: '1px solid #ccc', borderRadius: '4px', padding: '6px 12px' }}
                            />
                            <button
                                className='btn btn-danger'
                                onClick={dissableSale}
                            >
                                {selectedSale?.deactivation && selectedSale.deactivation !== "N/A" ? 'Actualizar fecha de baja' : 'Dar de baja'}
                            </button>
                        </div>
                    </div>

                    {/* Status Toggle Section */}
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                        <h5>Estado del Producto</h5>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <span>Estado actual: <strong>{selectedSale?.sale_detail_status === 1 ? 'Activo' : 'Inactivo'}</strong></span>
                            <button
                                className={`btn ${selectedSale?.sale_detail_status === 1 ? 'btn-warning' : 'btn-success'}`}
                                onClick={toggleItemStatus}
                            >
                                Cambiar a {selectedSale?.sale_detail_status === 1 ? 'Inactivo' : 'Activo'}
                            </button>
                        </div>
                        {selectedSale?.sale_detail_status === 1 && (
                            <small className="text-muted">
                                Nota: Para cambiar a inactivo se requiere una fecha de baja
                            </small>
                        )}
                    </div>

                    {/* Footer with close button */}
                    <div style={{ marginTop: 'auto', display: 'flex', justifyContent: 'flex-end' }}>
                        <button className="btn btn-secondary" onClick={() => setModalIsOpen(false)}>Cerrar</button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default SalesTable;