import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthProvider';
import { useNavigate } from 'react-router-dom';

import { AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai';
import toast, { Toaster } from 'react-hot-toast';

import { useLogin } from '../../hooks/auth/useLogin';

const LoginForm: React.FC = () => {
  const [values, setValues] = useState({
    username: '',
    password: '',
    email: '',
    showPassword: false,
    emailsend: false,
  });

  const { data: dataLogin, loading: loadingLogin, error: errorLogin, sendLoginRequest } = useLogin(values.username, values.password);
  const { login, isLoggedIn } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    sendLoginRequest();
  };

  useEffect(() => {
    if (isLoggedIn) {
      navigate('/dashboard');
    }

    if (loadingLogin) {
      toast.loading("Logging in...", { id: "login-loading-toast" });
    } else {
      toast.dismiss("login-loading-toast");
      if (dataLogin) {
        toast.success("Logged in successfully!");
        login(dataLogin, false);
      } else if (errorLogin) {
        toast.error("Error logging in!");
        login({ access_token: "" }, true);
      }
    }
  }, [isLoggedIn, dataLogin, loadingLogin, errorLogin]);



  return (
    <div className="d-flex justify-content-center align-items-center vh-100">
      <Toaster />
      <div className="container">
        <div className="row justify-content-center align-items-center h-100">
          <div className="col-md-8 col-lg-6">
            <h2 className="text-center mb-4">ACCOUNT LOGIN</h2>
            <form onSubmit={handleSubmit}>
              <div className="form-floating mb-3">
                <input
                  type="text"
                  className="form-control"
                  id="username"
                  placeholder="Username"
                  value={values.username}
                  onChange={(e) => setValues({ ...values, username: e.target.value })} />
                <label htmlFor="username">Username</label>
              </div>
              <div className="form-floating mb-3 position-relative">
                <input
                  type={values.showPassword ? "text" : "password"}
                  className="form-control"
                  id="password"
                  placeholder="Password"
                  value={values.password}
                  onChange={(e) => setValues({ ...values, password: e.target.value })}
                />
                <label htmlFor="password">Password</label>
                <span className="position-absolute top-50 end-0 translate-middle-y me-3" onClick={() => setValues({ ...values, showPassword: !values.showPassword })}>
                  {values.showPassword ? <AiOutlineEyeInvisible size={20} /> : <AiOutlineEye size={20} />}
                </span>
              </div>
              <button type="submit" className="btn btn-success w-100 mb-3">SIGN IN</button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;