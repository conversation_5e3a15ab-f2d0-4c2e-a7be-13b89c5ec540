import React, { useState, useEffect, ChangeEvent } from 'react';
import { useProducts } from '../hooks/db/useProducts';
import { MdLibraryAdd } from "react-icons/md";
import { useNavigate, Link } from 'react-router-dom';

interface Product {
  id: number;
  name: string;
  price: number;
  provider: string;
  product_id: string;
  status: number;
}

interface Bundle {
  id: number;
  name: string;
  total_price: number;
}

const Product: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('products');
  const [token] = useState<string | null>(localStorage.getItem('token'));
  const { data, loading, error, sendProductRequest } = useProducts(token as string);
  const [ivaPercentage, setIvaPercentage] = useState<number>(0);

  const [searchTermProduct, setSearchTermProduct] = useState<string>('');
  const [searchTermBundle, setSearchTermBundle] = useState<string>('');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [filteredBundles, setFilteredBundles] = useState<Bundle[]>([]);

  const [isHovered, setIsHovered] = useState(false);
  const buttonStyle: React.CSSProperties = {
    width: isHovered ? 'auto' : '50px',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    transition: 'max-width 0.3s ease-in-out, opacity 0.3s ease-in-out'
  };

  useEffect(() => {
    if (token) {
      sendProductRequest();
    }
  }, [token, sendProductRequest]);

  useEffect(() => {
    if (data) {
      const products: Product[] = data.products;
      const bundles: Bundle[] = data.bundles;

      const filteredProducts = searchTermProduct
        ? products.filter(product =>
          product.name.toLowerCase().includes(searchTermProduct.toLowerCase()) ||
          product.provider.toLowerCase().includes(searchTermProduct.toLowerCase()) ||
          product.price.toString().includes(searchTermProduct.toLowerCase()) ||
          product.product_id.toLowerCase().includes(searchTermProduct.toLowerCase())
        )
        : products;

      const filteredBundles = searchTermBundle
        ? bundles.filter(bundle =>
          bundle.name.toLowerCase().includes(searchTermBundle.toLowerCase()) ||
          bundle.total_price.toString().includes(searchTermBundle.toLowerCase())
        )
        : bundles;

      setFilteredProducts(filteredProducts);
      setFilteredBundles(filteredBundles);
    }
  }, [searchTermProduct, searchTermBundle, data, ivaPercentage]);

  const handleSearchChangeProduct = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchTermProduct(e.target.value);
  };

  const handleSearchChangeBundle = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchTermBundle(e.target.value);
  };

  const handleIvaChange = (e: ChangeEvent<HTMLInputElement>) => {
    setIvaPercentage(parseFloat(e.target.value) || 0);
  };

  const calculatePriceWithIva = (price: number): number => {
    return price + (price * ivaPercentage / 100);
  };

  return (
    <div className="container mt-4">
      <Link to="/products/create" className="btn btn-success position-fixed bottom-0 end-0 m-3"
        style={buttonStyle}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <MdLibraryAdd size={25} />
        {isHovered && <span className="ms-2">Añadir producto</span>}
      </Link>
      {loading && <div className="alert alert-info">Loading...</div>}
      {error && <div className="alert alert-danger">Error: {error.message}</div>}

      <ul className="nav nav-tabs">
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'products' ? 'active' : ''}`}
            onClick={() => setActiveTab('products')}
          >
            Catalogo
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'bundles' ? 'active' : ''}`}
            onClick={() => setActiveTab('bundles')}
          >
            Bundles
          </button>
        </li>
        {/* añadimos un inptu pequelo al lado derecho del todo */}
        <li className="nav-item ms-auto">
          <div className="input-group">
            <span className="input-group-text">IVA</span>
            <input
              type="number"
              className="form-control"
              placeholder="% IVA"
              value={ivaPercentage}
              onChange={handleIvaChange}
            />
          </div>
        </li>
      </ul>

      <div className="tab-content">
        <div className={`tab-pane fade mt-2 ${activeTab === 'products' ? 'show active' : ''}`}>
          <input
            type="text"
            className="form-control mb-2 mt-2"
            placeholder="Buscar Productos..."
            value={searchTermProduct}
            onChange={handleSearchChangeProduct}
          />
          <div className="table-responsive">
            <table className="table table table-hover">
              <thead>
                <tr>
                  <th>Estado</th>
                  <th>ID</th>
                  <th>Nombre</th>
                  <th>Precio</th>
                  <th>Proveedor</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map(product => (
                    <tr key={product.id} onClick={() => navigate(`/products/${product.id}`)} style={{ cursor: 'pointer' }}>
                      <th>{product.status === 1 ? '🟢' : '🔴'}</th>
                      <th>{product.product_id}</th>
                      <td>{product.name}</td>
                      <td>{calculatePriceWithIva(product.price).toFixed(2)} €</td>
                      <td>{product.provider}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4}>No se encontraron productos.</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className={`tab-pane fade mt-2 ${activeTab === 'bundles' ? 'show active' : ''}`}>
          <input
            type="text"
            className="form-control mb-2 mt-2"
            placeholder="Buscar Bundles..."
            value={searchTermBundle}
            onChange={handleSearchChangeBundle}
          />
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th>Nombre</th>
                  <th>Precio Total</th>
                </tr>
              </thead>
              <tbody>
                {filteredBundles.length > 0 ? (
                  filteredBundles.map(bundle => (
                    <tr key={bundle.id} onClick={() => navigate(`/bundles/${bundle.id}`)} style={{ cursor: 'pointer' }}>
                      <td>{bundle.name}</td>
                      <td>{calculatePriceWithIva(bundle.total_price).toFixed(2)} €</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={2}>No se encontraron paquetes.</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Product;
