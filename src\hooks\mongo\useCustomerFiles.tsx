import { useState, useEffect } from 'react';
import axios, { AxiosError } from 'axios';

interface CustomerFiles {
    zendesk_id: string;
    loading: boolean;
    error: AxiosError | null;
    data: any | null;
}

export const useCustomerFiles = (zendesk_id: string): CustomerFiles => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const [data, setData] = useState<any | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL;

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);
            setData(null);

            try {
                const headers = {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                };

                const response = await axios.get(`${apiUrl}/mongo/user/${zendesk_id}`, { headers });

                setData(response.data);
                // console.log(response.data);
                return response.data;
            } catch (error: unknown) {
                setError(error as AxiosError<unknown, any>);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    return { zendesk_id, loading, error, data };
}
