import { useState, useEffect } from 'react';
import { Sale } from '../../interfaces/sales';

export const useSale = (saleId: string) => {
    const [data, setData] = useState<Sale>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

  const apiUrl = import.meta.env.VITE_API_URL as string;

  const token = localStorage.getItem('token');

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const response = await fetch(`${apiUrl}/crm/sale/${saleId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                });
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const result = await response.json();
                const salesData: Sale = {
                    saleId: result.sale_id,
                    date: result.date.split('T')[0],
                    items: result.items.map((item: any) => ({
                        itemId: item.id.toString(),
                        name: item.name,
                        price: item.price,
                        type: item.type === 'Item' ? 'Sale' : 'Bundle',
                        bundleSales: item.bundleSales?.map((bundleSale: any) => ({
                            itemId: bundleSale.id.toString(),
                            name: bundleSale.name,
                            price: bundleSale.price,
                            type: bundleSale.type,
                            bundleSales: [],
                            ip: bundleSale.ip,
                            msisdn: bundleSale.msisdn,
                            sale_detail_id: bundleSale.sale_detail_id,
                        })),
                        ip: item.ip,
                        msisdn: item.msisdn,
                        sale_detail_id: item.sale_detail_id,
                    })),
                    user_id: result.user_id,
                }

                setData(salesData);
            } catch (error: any) {
                setError(error.message);
                // Create a single item with the error message
                setData({
                    saleId: '0',
                    date: '',
                    items: [{
                        itemId: '0',
                        name: "No items have been found",
                        price: 0,
                        type: 'Sale',
                        bundleSales: [],
                        ip: '',
                        msisdn: '',
                        sale_detail_id: '',
                    }],
                    user_id: '',
                });
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [saleId]);

    return { data, isLoading, error };
};