import { useState, useEffect } from 'react';
import axios from 'axios';

interface Email {
    id: string;
    subject: string;
}

export const useAllEmails = (param: number) => {
    const [data, setData] = useState<Email[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    useEffect(() => {
        const fetchData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const headers = {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                }

                const response = await axios.get(`${apiUrl}/crm/get-emails/${param}`, { headers });

                // Concatenate the new emails with the existing ones and remove duplicates
                setData(prevData => {
                    const allEmails = [...prevData, ...response.data];
                    const uniqueEmails = allEmails.filter((email, index, self) => 
                        index === self.findIndex((e) => e.id === email.id)
                    );
                    return uniqueEmails;
                });
            } catch (error: any) {
                setError(error.message);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [param]);

    return { data, isLoading, error };
};