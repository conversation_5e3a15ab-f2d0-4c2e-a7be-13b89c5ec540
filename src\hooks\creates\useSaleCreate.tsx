import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

export const useSaleCreate = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const [data, setData] = useState<any | null>(null);

  const apiUrl = import.meta.env.VITE_API_URL;

    const sendSaleRequest = useCallback(async (token: string, formData: any) => {
        setLoading(true);
        setError(null);
        setData(null);

        try {
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json',
              };

            const response = await axios.post(`${apiUrl}/crm/sale/create`, formData, { headers });

            setData(response.data);
            console.log(response.data);
        } catch (error: unknown) {
            setError(error as AxiosError<unknown, any>);
        } finally {
            setLoading(false);
        }
    }, []);

    return { loading, error, data, sendSaleRequest };
}