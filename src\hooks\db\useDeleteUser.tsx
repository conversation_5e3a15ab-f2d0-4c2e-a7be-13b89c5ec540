import { useState } from 'react';
import axios from 'axios';

export const useDeleteUser = () => {
    const [data, setData] = useState<any>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    const deleteUser = async (uuid: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const headers = {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
            }

            const response = await axios.delete(`${apiUrl}/crm/user/delete/${uuid}`, { headers });

            setData(response.data);
        } catch (error: any) {
            setError(error.message);
        } finally {
            setIsLoading(false);
        }
    };
    return { data, isLoading, error, deleteUser };
};