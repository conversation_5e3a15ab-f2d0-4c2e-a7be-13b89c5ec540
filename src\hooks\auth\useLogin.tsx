import { useState } from 'react';
import axios, { AxiosError } from 'axios';
import qs from 'qs';

interface LoginResponseData {
    access_token: string;
    token_type: string;
}

export const useLogin = (username: string | null, password: string | null) => {
    const [data, setData] = useState<LoginResponseData | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const apiUrl = import.meta.env.VITE_API_URL;

    const sendLoginRequest = async () => {
        if (!username || !password) {
            setError(new Error('Username or password is missing') as AxiosError);
            return;
        }

        try {
            setLoading(true);
            setError(null);
            const formData = qs.stringify({ username, password });
            const response = await axios.post<LoginResponseData>(`${apiUrl}/crm/login`, formData, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            setData(response.data);
        } catch (err) {
            setError(err as AxiosError);
        } finally {
            setLoading(false);
        }
    };

    return { data, loading, error, sendLoginRequest };
};
