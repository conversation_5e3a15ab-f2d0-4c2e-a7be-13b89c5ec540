import { useState, useCallback } from 'react';
import axios from 'axios';

export const useSendEmail = () => {
    const [isLoading, setIsLoading] = useState<boolean>(true); // Iniciar como true si esperas cargar datos inmediatamente
    const [error, setError] = useState<string | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL as string;

    const fetchData = useCallback(async (email_address: string, thread_id: string, body: string) => {
        setIsLoading(true);
        setError(null);

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error("Authentication token is missing");
            }
            const headers = {
                'Authorization': `Bearer ${token}`,
            };

            const response = await axios.post(`${apiUrl}/crm/email/reply`, {
                email_address: email_address,
                thread_id: thread_id,
                body: body,
            }, { headers });

            return response.data;
        } catch (error: any) {
            setError(error.response?.data?.message || error.message);
        } finally {
            setIsLoading(false);
        }
    }, [apiUrl]);

    return {isLoading, error, fetchData };
};
