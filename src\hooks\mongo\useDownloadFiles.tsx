import { useState, useCallback } from 'react';
import axios, { AxiosError } from 'axios';

interface CustomerFiles {
    loading: boolean;
    error: AxiosError | null;
    data: any | null;
    fetchData: (document_id: string, file_name: string) => Promise<any>;
}

export const useDownloadFiles = (): CustomerFiles => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<AxiosError | null>(null);
    const [data, setData] = useState<any | null>(null);

    const apiUrl = import.meta.env.VITE_API_URL;

    const fetchData = useCallback(async (document_id: string, file_name: string) => {
        setLoading(true);
        setError(null);
        setData(null);

        try {
            const headers = {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
            };

            const response = await axios.get(`${apiUrl}/mongo/user/download/${document_id}`, { headers, responseType: 'blob' });

            const contentDisposition = response.headers['content-disposition'];
            let filename = file_name;
            if (contentDisposition) {
                const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                let matches = filenameRegex.exec(contentDisposition);
                if (matches != null && matches[1]) { 
                    filename = matches[1].replace(/['"]/g, '');
                }
            }

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', filename);
            document.body.appendChild(link);
            link.click();

            return response.data;
        } catch (error: unknown) {
            setError(error as AxiosError<unknown, any>);
        } finally {
            setLoading(false);
        }
    }, [apiUrl]);

    return { loading, error, data, fetchData };
}