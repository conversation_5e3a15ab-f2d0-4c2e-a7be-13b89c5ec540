import React, { useEffect, useState } from 'react';
import DOMPurify from 'dompurify';
import { useEmailsUser } from '../hooks/emails/useEmailsUser';
import { useSendEmail } from '../hooks/emails/useSendEmail';

interface Email {
    Date: string;
    From: string;
    Subject: string;
    Body: string;
    id: string;
    threadId: string;
    historyId: string;
}

interface CustomerEmailsProps {
    customer_uuid: string;
}

const CustomerEmails: React.FC<CustomerEmailsProps> = ({ customer_uuid }) => {
    const { data, isLoading, error } = useEmailsUser(customer_uuid);
    const [replyBoxOpen, setReplyBoxOpen] = useState(false);
    const [replyText, setReplyText] = useState('');

    const { fetchData } = useSendEmail();

    useEffect(() => {
        console.log(data);
    }, [data]);

    if (isLoading) {
        return <p>Loading...</p>;
    }

    if (error) {
        return <p>Error: {error}</p>;
    }

    return (
        <div className="container mt-5">
            <h1> Correos </h1>

            {/* Show email cards */}
            <div className="row">
                {Array.isArray(data) && data.map((email: Email, index: number) => {
                    const emailThreads = email.Body.split(/De: .+ <.+>\r\nEnviado el: .+ \r\nPara: .+ <.+>\r\nAsunto:/);
                    const secondResponse = emailThreads.length > 1 ? emailThreads[1] : '';
                    const displayThreads = emailThreads.filter((thread: string) => thread !== secondResponse); // Add type here

                    return (
                        <div className="col-12" key={index}>
                            <div className="card mt-3 shadow-sm">
                                <div className="card-header">
                                    <h5 className="mb-0"> {email.Subject} </h5>
                                </div>
                                <div className="card-body">
                                    <p className="mb-1"><strong>From:</strong> {email.From} </p>
                                    <p className="mb-2"><strong>Date:</strong> {email.Date} </p>
                                    <hr />
                                    {displayThreads.map((thread: string, threadIndex: number) => (
                                        <div key={threadIndex} dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(thread.split('\n').join('<br/>')) }} />
                                    ))}
                                    <hr />
                                    <button className="btn btn-primary" onClick={() => setReplyBoxOpen(true)}>Reply</button>
                                    {replyBoxOpen && (
                                        <div className="mt-2">
                                            <textarea className="form-control" value={replyText} onChange={e => setReplyText(e.target.value)} />
                                            <button className="btn btn-success mt-2" onClick={() => 
                                            { 
                                                fetchData(email.From, email.threadId, replyText);
                                            }}>Send</button>
                                            
                                            <button className="btn btn-danger mt-2 ml-2" onClick={() => setReplyBoxOpen(false)}>Cancel</button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default CustomerEmails;
